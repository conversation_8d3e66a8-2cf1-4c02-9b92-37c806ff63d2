/**
 * @jest-environment node
 */
import type { NextResponse } from 'next/server'
import { NextRequest } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { Locale } from '@/constants/locale'
import { envVars } from '@/env'
import { localeMiddleware } from '@/middlewares/localeMiddleware'
import { authService } from '@/services/auth.service'
import { detectLocaleByRequest } from '@/utils/server/network'

jest.mock('@/utils/server/network', () => ({
  ...jest.requireActual('@/utils/server/network'),
  detectLocaleByRequest: jest.fn(),
}))

jest.mock('@/services/auth.service', () => ({
  authService: {
    isAuthenticated: jest.fn(),
  },
}))

jest.mock('@/env', () => ({
  envVars: {
    ...jest.requireActual('@/env').envVars,
    NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH: false,
  },
}))

jest.mock('@/config/serverConfig', () => ({
  ...jest.requireActual('@/config/serverConfig'),
}))

// Mock the console.warn to avoid test output noise
const originalConsoleWarn = console.warn
beforeAll(() => {
  console.warn = jest.fn()
})

afterAll(() => {
  console.warn = originalConsoleWarn
})

describe('localeMiddleware', () => {
  const mockRequest = (url: string) => new NextRequest(new URL(url))

  const isRedirectResponse = (response: any): response is NextResponse => {
    return response && typeof response.headers?.get === 'function'
  }

  const isRewriteResponse = (response: any): response is { isRewrite: boolean; response: NextResponse } => {
    return response && typeof response.isRewrite === 'boolean'
  }
  beforeEach(() => {
    jest.clearAllMocks()
    envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH = false
  })

  describe('when disabled NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH', () => {
    beforeEach(() => {
      jest.clearAllMocks()
      envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH = false
    })

    it('redirects to default locale if no locale is detected', async () => {
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue(null)
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/en/some-path')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('redirects to detected locale if supported', async () => {
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca')
      serverConfig.defaultLocale = Locale.CA_FR
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/ca-fr/some-path')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('redirects to default locale if detected locale is unsupported', async () => {
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('es')
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/en/some-path')
      } else {
        fail('Expected a redirect response')
      }
      expect(console.warn).toHaveBeenCalledWith('Detected locale es is not supported. Defaulting to en.')
    })

    it('redirects authenticated users to their locale', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca')
      serverConfig.defaultLocale = Locale.CA_FR
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/ca-fr/some-path')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('redirects unauthenticated users with unsupported locale prefix to detected locale', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca')
      serverConfig.defaultLocale = Locale.CA_FR
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/es/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/ca-fr/some-path')
      } else {
        fail('Expected a redirect response')
      }
    })

    // Additional test cases for better coverage
    it('does not redirect for paths containing file extensions', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/assets/styles.css')
      const response = await localeMiddleware(request)

      expect(response).toBeUndefined()
    })

    it('does not redirect for paths already containing supported locale', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/en/some-path')
      const response = await localeMiddleware(request)

      expect(response).toBeUndefined()
    })

    it('does not redirect authenticated users if path already contains their locale', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca-fr')
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/ca-fr/dashboard1')
      const response = await localeMiddleware(request)

      expect(response).toBeUndefined()
    })

    it('redirects authenticated users with incorrect locale prefix to their locale', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca')
      serverConfig.defaultLocale = Locale.CA_FR
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/en/dashboard')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/ca-fr/dashboard')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('redirects authenticated users to default locale when their detected locale is unsupported', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('es')
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/es/dashboard')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/en/dashboard')
      } else {
        fail('Expected a redirect response')
      }
      expect(console.warn).toHaveBeenCalledWith('Detected locale es is not supported. Defaulting to en.')
    })
  })

  describe('when enabled NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH', () => {
    beforeEach(() => {
      envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH = true
    })

    it('redirects default locale path to path without locale prefix', async () => {
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/en/dashboard')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/dashboard')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('redirects default locale root path to root without locale prefix', async () => {
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/en/')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('internally rewrites root path to default locale path', async () => {
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue(null)
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRewriteResponse(response)) {
        expect(response.isRewrite).toBe(true)
        // The rewrite response should contain a NextResponse pointing to the localized path
        expect(response.response).toBeDefined()
      } else {
        fail('Expected a rewrite response')
      }
    })

    it('internally rewrites non-localized path to default locale path', async () => {
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue(null)
      serverConfig.defaultLocale = Locale.EN
      serverConfig.supportedLocales = [Locale.EN, Locale.CA_FR]

      const request = mockRequest('http://example.com/dashboard')
      const response = await localeMiddleware(request)

      expect(response).toBeDefined()
      if (isRewriteResponse(response)) {
        expect(response.isRewrite).toBe(true)
        expect(response.response).toBeDefined()
      } else {
        fail('Expected a rewrite response')
      }
    })
  })
})
