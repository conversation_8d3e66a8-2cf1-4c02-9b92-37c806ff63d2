import { NextResponse, type NextRequest } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { envVars } from '@/env'
import { localeUrlSegmentRegex, supportedLocaleRegex } from '@/middlewares/utils'
import { authService } from '@/services/auth.service'
import { detectLocaleByRequest, extractLocaleFromUrl } from '@/utils/server/network'
import type { Locale } from '@constants/locale'

function replaceLocaleInPath(pathname: string, currentLocale: string) {
  if (localeUrlSegmentRegex.test(pathname)) {
    return pathname.replace(localeUrlSegmentRegex, `/${currentLocale}`)
  }
  return `/${currentLocale}${pathname}`
}

function handleDefaultLocaleRemoval(pathname: string) {
  const currentLocale = extractLocaleFromUrl(pathname)
  if (envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH && currentLocale === serverConfig.defaultLocale) {
    // If the current locale is the default locale and we are omitting it from the path
    return pathname.replace(`/${currentLocale}`, '')
  }
}

export async function localeMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  const defaultLocaleOmissionUrl = handleDefaultLocaleRemoval(pathname)
  if (defaultLocaleOmissionUrl !== undefined) {
    return NextResponse.redirect(new URL(defaultLocaleOmissionUrl || '/', request.url))
  }

  const isNormalizedRoute = pathname.includes('.') || supportedLocaleRegex.test(pathname)

  if (!isNormalizedRoute) {
    let currentLocale = (await detectLocaleByRequest(request)) || serverConfig.defaultLocale

    if (!serverConfig.supportedLocales.includes(currentLocale as Locale)) {
      console.warn(`Detected locale ${currentLocale} is not supported. Defaulting to ${serverConfig.defaultLocale}.`)
      currentLocale = serverConfig.defaultLocale
    }

    // Handle root path - rewrite internally without changing URL
    if (envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH && currentLocale === serverConfig.defaultLocale) {
      if (pathname === '/' || !supportedLocaleRegex.test(pathname)) {
        const newPath = `/${currentLocale}${pathname}`
        const response = NextResponse.rewrite(new URL(newPath, request.url))
        return { isRewrite: true, response } // Return an object to indicate rewrite
      }
    }
    const newPath = replaceLocaleInPath(pathname, currentLocale)
    return NextResponse.redirect(new URL(newPath, request.url))
  }

  const isAuthenticated = await authService.isAuthenticated()

  if (isAuthenticated) {
    let userLocale = (await detectLocaleByRequest(request)) || serverConfig.defaultLocale
    if (!serverConfig.supportedLocales.includes(userLocale as Locale)) {
      console.warn(`Detected locale ${userLocale} is not supported. Defaulting to ${serverConfig.defaultLocale}.`)
      userLocale = serverConfig.defaultLocale
    }

    // Handle cases like '/ee/dashboard' or '/mk/ee/dashboard'
    if (!pathname.startsWith(`/${userLocale}`)) {
      const newPath = replaceLocaleInPath(pathname, userLocale)
      return NextResponse.redirect(new URL(newPath, request.url))
    }
  }
}
