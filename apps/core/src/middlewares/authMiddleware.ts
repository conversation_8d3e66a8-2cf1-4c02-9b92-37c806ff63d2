import { NextResponse, type NextRequest } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { detectLocaleByRequest } from '@/utils/server/network'

export async function authMiddleware(request: NextRequest) {
  const publicRoutes = ['^/$', ...serverConfig.publicRoutes]
  const supportedLocales = serverConfig.supportedLocales
  const { pathname } = request.nextUrl
  const isPublicRoute = publicRoutes.some(publicRoute => {
    const pattern = publicRoute.startsWith('^') ? publicRoute : `^${publicRoute}(?:/|$)`
    return (
      new RegExp(pattern).test(pathname) ||
      supportedLocales.some(locale => {
        const localePattern = publicRoute.startsWith('^')
          ? `^/${locale}$|^/${locale}/$`
          : `^/${locale}${publicRoute}(?:/|$)`
        return new RegExp(localePattern).test(pathname)
      })
    )
  })

  if (pathname.endsWith('/login')) {
    const isAuthenticated = await authService.isAuthenticated()
    if (isAuthenticated) {
      const currentLocale = await detectLocaleByRequest(request)
      const appRouter = getAppRouter(currentLocale)
      return NextResponse.redirect(new URL(appRouter.home, request.url))
    }
  }

  if (!isPublicRoute) {
    const isAuthenticated = await authService.isAuthenticated()
    if (!isAuthenticated) {
      const currentLocale = await detectLocaleByRequest(request)
      console.info('[Auth Middleware] Redirect to login', { currentLocale }, await detectLocaleByRequest(request))
      const appRouter = getAppRouter(currentLocale)
      return NextResponse.redirect(new URL(appRouter.login, request.url))
    }
  }
}
