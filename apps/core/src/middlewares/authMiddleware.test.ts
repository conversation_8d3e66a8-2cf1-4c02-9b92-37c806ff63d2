/**
 * @jest-environment node
 */
import type { NextResponse } from 'next/server'
import { NextRequest } from 'next/server'
import { authMiddleware } from '@/middlewares/authMiddleware'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { detectLocaleByRequest } from '@/utils/server/network'

jest.mock('@/services/auth.service', () => ({
  authService: {
    isAuthenticated: jest.fn(),
  },
}))

jest.mock('@/services/router.service', () => ({
  getAppRouter: jest.fn(() => ({
    home: '/casino',
    login: '/login',
  })),
}))

jest.mock('@/utils/server/network', () => ({
  detectLocaleByRequest: jest.fn(),
}))

jest.mock('@/config/serverConfig', () => ({
  serverConfig: {
    publicRoutes: ['/', '/login', '/register', '/casino', '/live-casino', '/promotions', '/game'],
    supportedLocales: ['en', 'us', 'ca-fr'],
  },
}))

// Mock console.info to avoid test output noise
const originalConsoleInfo = console.info
beforeAll(() => {
  console.info = jest.fn()
})

afterAll(() => {
  console.info = originalConsoleInfo
})

describe('authMiddleware', () => {
  const mockRequest = (url: string) => new NextRequest(new URL(url))

  const isRedirectResponse = (response: any): response is NextResponse => {
    return response && typeof response.headers?.get === 'function'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('en')
  })

  describe('Public Route Detection', () => {
    describe('Root path handling', () => {
      it('should recognize "/" as a public route', async () => {
        const request = mockRequest('http://example.com/')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/en" as a public route (localized root)', async () => {
        const request = mockRequest('http://example.com/en')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/en/" as a public route (localized root with trailing slash)', async () => {
        const request = mockRequest('http://example.com/en/')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/us" as a public route (different locale)', async () => {
        const request = mockRequest('http://example.com/us')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/ca-fr" as a public route (hyphenated locale)', async () => {
        const request = mockRequest('http://example.com/ca-fr')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })
    })

    describe('Other public routes', () => {
      it('should recognize "/register" as a public route', async () => {
        const request = mockRequest('http://example.com/register')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/ca-fr/casino" as a public route', async () => {
        const request = mockRequest('http://example.com/ca-fr/casino')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/promotions" as a public route', async () => {
        const request = mockRequest('http://example.com/promotions')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/game" as a public route', async () => {
        const request = mockRequest('http://example.com/game')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })

      it('should recognize "/us/game/slots" as a public route (sub-path of public route)', async () => {
        const request = mockRequest('http://example.com/us/game/slots')
        const response = await authMiddleware(request)

        expect(response).toBeUndefined()
        expect(authService.isAuthenticated).not.toHaveBeenCalled()
      })
    })

    describe('Private routes', () => {
      it('should require authentication for private routes', async () => {
        ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
        ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('en')

        const request = mockRequest('http://example.com/dashboard')
        const response = await authMiddleware(request)

        expect(authService.isAuthenticated).toHaveBeenCalled()
        expect(response).toBeDefined()
        if (isRedirectResponse(response)) {
          expect(response.headers.get('location')).toBe('http://example.com/login')
        } else {
          fail('Expected a redirect response')
        }
      })

      it('should require authentication for localized private routes', async () => {
        ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
        ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca-fr')

        const request = mockRequest('http://example.com/ca-fr/dashboard')
        const response = await authMiddleware(request)

        expect(authService.isAuthenticated).toHaveBeenCalled()
        expect(response).toBeDefined()
        if (isRedirectResponse(response)) {
          expect(response.headers.get('location')).toBe('http://example.com/login')
        } else {
          fail('Expected a redirect response')
        }
      })

      it('should allow access to private routes when authenticated', async () => {
        ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)

        const request = mockRequest('http://example.com/dashboard')
        const response = await authMiddleware(request)

        expect(authService.isAuthenticated).toHaveBeenCalled()
        expect(response).toBeUndefined()
      })
    })
  })

  describe('Login route handling', () => {
    it('should redirect authenticated users away from login page', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('en')

      const request = mockRequest('http://example.com/login')
      const response = await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalled()
      expect(detectLocaleByRequest).toHaveBeenCalledWith(request)
      expect(getAppRouter).toHaveBeenCalledWith('en')
      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/casino')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('should redirect authenticated users away from localized login page', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca-fr')

      const request = mockRequest('http://example.com/ca-fr/login')
      const response = await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalled()
      expect(detectLocaleByRequest).toHaveBeenCalledWith(request)
      expect(getAppRouter).toHaveBeenCalledWith('ca-fr')
      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/casino')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('should allow unauthenticated users to access login page', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)

      const request = mockRequest('http://example.com/login')
      const response = await authMiddleware(request)

      expect(response).toBeUndefined()
    })

    it('should handle complex login paths (e.g., with query params)', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('us')

      const request = mockRequest('http://example.com/some/path/login?redirect=/dashboard')
      const response = await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalled()
      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/casino')
      } else {
        fail('Expected a redirect response')
      }
    })
  })

  describe('Edge cases', () => {
    it('should handle paths that start with public routes but are not exact matches', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)

      // "/gaming" should not match "/game" public route
      const request = mockRequest('http://example.com/gaming')
      const response = await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalled()
      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/login')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('should handle paths with trailing slashes correctly', async () => {
      const request = mockRequest('http://example.com/casino/')
      const response = await authMiddleware(request)

      expect(response).toBeUndefined()
      expect(authService.isAuthenticated).not.toHaveBeenCalled()
    })

    it('should handle case sensitivity correctly', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)

      const request = mockRequest('http://example.com/LOGIN')
      const response = await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalled()
      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('http://example.com/login')
      } else {
        fail('Expected a redirect response')
      }
    })

    it('should log redirect information for unauthenticated access', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('en')

      const request = mockRequest('http://example.com/dashboard')
      await authMiddleware(request)

      expect(console.info).toHaveBeenCalledWith('[Auth Middleware] Redirect to login', { currentLocale: 'en' }, 'en')
    })
  })

  describe('Locale detection integration', () => {
    it('should use detected locale for router service', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('ca-fr')

      const request = mockRequest('http://example.com/login')
      await authMiddleware(request)

      expect(detectLocaleByRequest).toHaveBeenCalledWith(request)
      expect(getAppRouter).toHaveBeenCalledWith('ca-fr')
    })

    it('should handle null locale from detection', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue(null)

      const request = mockRequest('http://example.com/login')
      const response = await authMiddleware(request)

      expect(detectLocaleByRequest).toHaveBeenCalledWith(request)
      expect(getAppRouter).toHaveBeenCalledWith(null)
      expect(response).toBeDefined()
    })
  })

  describe('Authentication service integration', () => {
    it('should call authentication service for protected routes', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)

      const request = mockRequest('http://example.com/dashboard')
      await authMiddleware(request)

      expect(authService.isAuthenticated).toHaveBeenCalledTimes(1)
    })

    it('should call authentication service twice for login pages (once for login check, once for auth check)', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)

      const request = mockRequest('http://example.com/login')
      await authMiddleware(request)

      // Only called once because it's a public route, so the second check doesn't happen
      expect(authService.isAuthenticated).toHaveBeenCalledTimes(1)
    })

    it('should handle authentication service errors gracefully', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockRejectedValue(new Error('Auth service error'))

      const request = mockRequest('http://example.com/dashboard')

      await expect(authMiddleware(request)).rejects.toThrow('Auth service error')
    })
  })

  describe('URL construction', () => {
    it('should handle complex URLs with fragments', async () => {
      ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
      ;(detectLocaleByRequest as jest.Mock).mockResolvedValue('us')

      const request = mockRequest('https://example.com/login#section1')
      const response = await authMiddleware(request)

      expect(response).toBeDefined()
      if (isRedirectResponse(response)) {
        expect(response.headers.get('location')).toBe('https://example.com/casino')
      } else {
        fail('Expected a redirect response')
      }
    })
  })
})
