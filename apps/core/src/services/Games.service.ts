import 'server-only'
import { s3F<PERSON>ch<PERSON><PERSON> } from '@/network/server-utils/s3/S3FetchApi'
import { cacheService, withCache } from '@/services/Cache.service'
import { filterGamesRestrictions } from '@repo/helpers/gamesHelper'
import type { IRlGame } from '@repo/types/games'

type PrepareGamesOptions = {
  countryByIp: string
  regionOfAction: string
  license: string
  countryOfRegistration?: string
}

class GamesService {
  private CACHE_REVALIDATE_TIME_SECONDS = 10
  private GAMES_CACHE_KEY_PREFIX = 'games'

  async getAllGames() {
    const cacheKey = cacheService.buildCacheKey([this.GAMES_CACHE_KEY_PREFIX, 'allGames'])
    const allGames = await withCache(
      cacheKey,
      async () => {
        const result = await s3FetchApi.getAllGamesList()
        const keys = cacheService.getKeysStartsWith(this.GAMES_CACHE_KEY_PREFIX + cacheService.cacheKeySeparator)

        cacheService.invalidate(keys)
        return result
      },
      this.CACHE_REVALIDATE_TIME_SECONDS,
    )
    if (!Array.isArray(allGames) || allGames.length === 0) {
      cacheService.invalidate(cacheKey)
    }
    return allGames
  }

  async prepareGames(
    games: IRlGame[],
    { countryByIp, regionOfAction, countryOfRegistration, license }: PrepareGamesOptions,
  ) {
    const cacheKey = cacheService.buildCacheKey([
      this.GAMES_CACHE_KEY_PREFIX,
      'prepareGames',
      countryByIp,
      regionOfAction,
      countryOfRegistration || 'none',
      license,
      games.length,
    ])

    return withCache(
      cacheKey,
      async () => {
        console.debug('[GamesService] preparing games', { countryByIp, regionOfAction, countryOfRegistration, license })
        console.debug('[GamesService] games before restrictions filtering:', games?.length)

        const t1 = Date.now()
        // await new Promise(resolve => setTimeout(resolve, 5000)) // imitate long operation like filtering, sorting, etc.
        const restrictionsFilteredGames = await filterGamesRestrictions(
          [...games],
          {
            countryByIp,
            regionOfAction,
            countryOfRegistration,
          },
          license,
          countryByIp,
        )
        const t2 = Date.now()
        console.debug('[GamesService] games processing time:', t2 - t1, 'ms')
        console.debug('[GamesService] games after restrictions filtering:', restrictionsFilteredGames.length)

        return restrictionsFilteredGames || []
      },
      this.CACHE_REVALIDATE_TIME_SECONDS,
    )
  }

  async prepareGamesMapById(games: IRlGame[], props: PrepareGamesOptions) {
    const cacheKey = cacheService.buildCacheKey([
      this.GAMES_CACHE_KEY_PREFIX,
      'prepareGamesMapById',
      props.countryByIp,
      props.regionOfAction,
      props.countryOfRegistration || 'none',
      props.license,
      games.length,
    ])

    return withCache(
      cacheKey,
      async () => {
        const restrictionsFilteredGames = await this.prepareGames(games, props)
        return Object.fromEntries(restrictionsFilteredGames.map(game => [game.id, game])) || {}
      },
      this.CACHE_REVALIDATE_TIME_SECONDS,
    )
  }

  async prepareGamesMapBySlug(games: IRlGame[], props: PrepareGamesOptions) {
    const cacheKey = cacheService.buildCacheKey([
      this.GAMES_CACHE_KEY_PREFIX,
      'prepareGamesMapBySlug',
      props.countryByIp,
      props.regionOfAction,
      props.countryOfRegistration || 'none',
      props.license,
      games.length,
    ])

    return withCache(
      cacheKey,
      async () => {
        const restrictionsFilteredGames = await this.prepareGames(games, props)
        return Object.fromEntries(restrictionsFilteredGames.map(game => [game.slug, game])) || {}
      },
      this.CACHE_REVALIDATE_TIME_SECONDS,
    )
  }
}

export const gamesService = new GamesService()
