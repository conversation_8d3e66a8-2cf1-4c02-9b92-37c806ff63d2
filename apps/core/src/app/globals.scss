@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

html {
  background-color: $color-background;
  max-width: 100vw;
  overflow-x: hidden;
  color: $color-surface-1000;
  font-size: 16px;
  line-height: 1.15;
}

.dark [data-hide-on-theme='dark'],
.light [data-hide-on-theme='light'] {
  display: none;
}

h1 {
  font-size: calculate-rem(40px);
  font-weight: 700;
  color: $color-on-secondary;
}
h2 {
  font-size: calculate-rem(32px);
  font-weight: 700;
  color: $color-on-secondary;
}
h3 {
  font-size: calculate-rem(28px);
  font-weight: 700;
  color: $color-on-secondary;
}
h4 {
  font-size: calculate-rem(24px);
  font-weight: 700;
  color: $color-on-secondary;
}

pre {
  font-family: var(--default-font-family); /* TODO: Align font use across app */
}

img {
  height: 100%;
}

*::-webkit-scrollbar {
  height: calculate-rem(6px);
  width: calculate-rem(6px);
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: $color-surface-200;
  border-radius: calculate-rem(3px);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: $color-surface-700;
}
