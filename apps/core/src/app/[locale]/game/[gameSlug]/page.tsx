import { getGamePageConfig } from '@/network/server-utils/s3/getters'
import type { PageProps, RouteParams } from '@/types/nextjs'
import DynamicContentRenderer from '@components/DynamicContentRenderer/DynamicContentRenderer'
import { PageContainer } from '@modules/page'

export const revalidate = 10

export default async function GamePage({ params }: PageProps<RouteParams & { gameSlug: string }>) {
  const requestParams = await params
  const config = await getGamePageConfig()

  if (!config) {
    console.error('[GamePage] Configuration not found')
    return null
  }

  return (
    <PageContainer>
      <DynamicContentRenderer config={config} locale={requestParams.locale} requestParams={requestParams} />
    </PageContainer>
  )
}
