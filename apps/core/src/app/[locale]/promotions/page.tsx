import React from 'react'
import type { Metadata } from 'next'
import { getPromotionsPageConfig } from '@/network/server-utils/s3/getters'
import { authService } from '@/services/auth.service'
import type { RouteParams } from '@/types/nextjs'
import { getLicenseByLocale } from '@/utils/locale'
import DynamicContentRenderer from '@components/DynamicContentRenderer/DynamicContentRenderer'
import { PageContainer } from '@modules/page'
import { PromotionsSettings } from '@modules/promotions'
import { RHINOLAYER_TARGET_KEY, RHINOLAYER_TARGET_VALUES } from '@repo/constants/common/rhinoLayer'
import type { IGetPromotionsProps } from '@repo/types/api/rl/promotions'
import { PromotionsScreen } from '@screens/promotions/PromotionsScreen'

type Params = Promise<RouteParams>

export const metadata: Metadata = {
  title: 'Promotions',
  description: 'Promotions page',
}

export const revalidate = 10

export default async function PromotionsPage({
  params,
}: Readonly<{
  params: Params
}>) {
  const { locale } = await params
  const isLoggedIn = await authService.isAuthenticated()

  const promotionsPageConfig = await getPromotionsPageConfig()

  if (!promotionsPageConfig) return null

  /* TODO: Remove Existing promotions implementation */

  const requestParams: IGetPromotionsProps = {
    locale,
    license: getLicenseByLocale(locale),
    tags: [
      `${RHINOLAYER_TARGET_KEY}:${RHINOLAYER_TARGET_VALUES.ALL}`,
      `${RHINOLAYER_TARGET_KEY}:${
        isLoggedIn ? RHINOLAYER_TARGET_VALUES.LOGGED_IN : RHINOLAYER_TARGET_VALUES.LOGGED_OUT
      }`,
    ],
    isLoggedIn,
    type: 0,
    limit: PromotionsSettings.PER_PAGE,
    page: 1,
  }
  //   const promotions = isLoggedIn ? undefined : await rlFetchApi.getPromotions(requestParams)
  //   console.log('SS promotions fetched:', promotions?.payload?.length, requestParams)

  return (
    <>
      <PageContainer>
        {promotionsPageConfig ? <DynamicContentRenderer config={promotionsPageConfig} locale={locale} /> : null}

        {/* TODO: Remove Existing promotions implementation */}
        <PromotionsScreen requestParams={requestParams} initialData={undefined} locale={locale} />
      </PageContainer>
    </>
  )
}
