import { headers } from 'next/headers'
import { serverConfig } from '@/config/serverConfig'
import { LocaleProvider } from '@/context/locale/LocaleProvider'
import { getAppConfig, getWelcomePageConfig } from '@/network/server-utils/s3/getters'
import type { LayoutProps } from '@/types/nextjs'
import { getMarketByLocale } from '@/utils/locale'
import { Footer } from '@modules/footer'
import { Header } from '@modules/header'
import { Sidebars } from '@modules/sidebars'
import { detectDeviceType } from '@repo/helpers/deviceHelpers'
import { SidebarProvider } from '@repo/ui/shadcn/sidebar'
import styles from '@app/[locale]/layout.module.scss'

export async function generateMetadata({ params }: LayoutProps) {
  // read route params
  const { locale } = await params
  const market = getMarketByLocale(locale)
  const welcomePageConfig = await getWelcomePageConfig(locale)
  const appConfig = await getAppConfig(locale)

  return {
    title: serverConfig.appName + ` | ${welcomePageConfig?.seo_title || market}`,
    description: welcomePageConfig?.seo_description,
  }
}

export async function generateStaticParams() {
  //  const posts = await fetch('https://.../posts').then(res => res.json())
  return serverConfig.supportedLocales.map(locale => ({ locale }))
}

export const dynamicParams = false

export default async function MainLayout({ children, modal, params }: LayoutProps) {
  const { locale } = await params
  const _headers = await headers()
  const deviceType = detectDeviceType(_headers.get('user-agent') || '')

  return (
    <>
      <LocaleProvider locale={locale}>
        <SidebarProvider className={styles.container} deviceType={deviceType}>
          <Header deviceType={deviceType} />
          <div className={styles.main}>
            <Sidebars locale={locale}>
              {children}
              <Footer />
            </Sidebars>
          </div>
          {modal}
        </SidebarProvider>
      </LocaleProvider>
    </>
  )
}
