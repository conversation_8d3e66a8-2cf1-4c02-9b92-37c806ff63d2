import { withSuspense } from '@/HOC/withSuspense'
import type { PageProps } from '@/types/nextjs'
import { Banner } from '@components/Banner'
import { PageContainer, PageTitle } from '@modules/page'
import { LobbyType } from '@repo/constants/common/lobby'
import { LobbyScreen } from '@screens/lobby/LobbyScreen'

export const revalidate = 10

export default async function CasinoPage({ params }: PageProps) {
  const { locale } = await params
  // const isAuthenticated = await authService.isAuthenticated()
  return (
    <>
      <div>
        <Banner
          badge={{
            label: 'Badge Label',
            iconSrc:
              'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
          }}
          title="Welcome to Luckyone.us – your gateway to exciting rewards!"
          subtitle="Make your first purchase today and unlock exclusive free spins"
          iconTitleSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner-icon.png"
          backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
          ctaButtons={[
            {
              label: 'Claim',
              href: '#',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
              color: 'primary',
            },
            {
              href: '#',
              icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
              icon_dark:
                'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
              color: 'primary',
            },
          ]}
          variant="padded"
        />
      </div>

      <PageContainer>
        <PageTitle title={'Casino Page'} />
        {withSuspense(<LobbyScreen locale={locale} type={LobbyType.Casino} />)}
      </PageContainer>
    </>
  )
}
