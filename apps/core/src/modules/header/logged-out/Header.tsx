'use client'
import React from 'react'
import { usePathname } from 'next/navigation'
import { useAppRouter } from '@/services/router.service'
import { Button } from '@components/Button'
import { DynamicLink } from '@components/DynamicLink'
import { ThemeToggle } from '@components/ThemeToggle'
import { NavbarContent, NavbarItem } from '@heroui/navbar'
import { HeaderNavigation } from '@modules/header/components'

export const LoggedOutHeader: React.FC = () => {
  const appRouter = useAppRouter()
  const pathname = usePathname()
  const isOnLoginPage = pathname.endsWith('/login')
  const isOnRegisterPage = pathname.endsWith('/register')

  return (
    <>
      <HeaderNavigation />
      <NavbarContent justify="end">
        <NavbarItem>
          <ThemeToggle />
        </NavbarItem>
        <NavbarItem>
          <Button
            as={DynamicLink}
            href={appRouter.login}
            scroll={false}
            isDisabled={isOnLoginPage}
            size="sm"
            className="md:text-medium! md:h-10! md:px-5!">
            Login
          </Button>
        </NavbarItem>
        <NavbarItem>
          <Button
            as={DynamicLink}
            href={appRouter.register}
            variant="solid"
            scroll={false}
            color="secondary"
            isDisabled={isOnRegisterPage}
            size="sm"
            className="md:text-medium! md:h-10! md:px-5!">
            Register
          </Button>
        </NavbarItem>
      </NavbarContent>
    </>
  )
}
