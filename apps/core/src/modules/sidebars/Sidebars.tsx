import type { <PERSON> } from 'react'
import React from 'react'
import { getSidebarConfig } from '@/network/server-utils/s3/getters'
import type { Locale } from '@constants/locale'
import { Sidebar } from '@modules/sidebars/Sidebar'
import { SidebarSideEnum } from '@repo/constants/common/sidebar'
import { SidebarInset } from '@repo/ui/shadcn/sidebar'

interface ISidebarsProps {
  children: React.ReactNode
  locale: Locale
}

const Sidebars: FC<ISidebarsProps> = async ({ children, locale }) => {
  // TODO: Handle per device
  const sidebarsConfig = await getSidebarConfig()
  const leftSidebarConfig = sidebarsConfig?.left
  const rightSidebarConfig = sidebarsConfig?.right

  return (
    <>
      {!!leftSidebarConfig && <Sidebar side={SidebarSideEnum.LEFT} locale={locale} />}
      <SidebarInset>{children}</SidebarInset>
      {!!rightSidebarConfig && <Sidebar side={SidebarSideEnum.RIGHT} locale={locale} />}
    </>
  )
}

export default Sidebars
