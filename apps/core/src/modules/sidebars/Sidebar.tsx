import type { <PERSON> } from 'react'
import React from 'react'
import { getSidebarConfig } from '@/network/server-utils/s3/getters'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import type { Locale } from '@constants/locale'
import { SidebarClient } from '@modules/sidebars/Sidebar.client'
import type { SidebarSideEnum } from '@repo/constants/common/sidebar'

interface ISidebarProps {
  side: SidebarSideEnum
  locale: Locale
}

export const Sidebar: FC<ISidebarProps> = async ({ side, locale }) => {
  const sidebarsConfig = await getSidebarConfig()
  const sidebarConfig = sidebarsConfig?.[side]

  if (!sidebarConfig) {
    // already validated on previous step
    return null
  }

  return (
    <SidebarClient side={side}>
      <DynamicContentRenderer config={sidebarConfig} locale={locale} />
    </SidebarClient>
  )
}
