import type { FC } from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { ChatClient } from '@modules/chat/Chat.client'
import { getChatData } from '@modules/chat/ChatData'

const Chat: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  const data = await getChatData()

  return <ChatClient {...data} />
}

export default Chat
