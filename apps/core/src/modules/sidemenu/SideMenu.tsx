import type { FC } from 'react'
import { getSidemenuConfig } from '@/network/server-utils/s3/getters'
import { DynamicContentRenderer } from '@components/DynamicContentRenderer'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

const SideMenu: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  const config = await getSidemenuConfig()

  if (!config) {
    console.error('[SideMenu] Configuration not found')
    return null
  }

  return <DynamicContentRenderer config={config} locale={locale} />
}

export default SideMenu
