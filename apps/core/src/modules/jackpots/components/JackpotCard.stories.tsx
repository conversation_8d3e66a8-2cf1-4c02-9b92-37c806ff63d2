import { JackpotCard } from '@modules/jackpots/components'
import type { Meta, StoryObj } from '@storybook/react'

const meta: Meta<typeof JackpotCard> = {
  title: 'Modules/Jackpots/JackpotCard',
  component: JackpotCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A jackpot card component displaying jackpot details',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    type: 'Grand',
    amount: 491098.35,
    currency: 'SC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won a month ago',
  },
}

export default meta
type Story = StoryObj<typeof JackpotCard>

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different variations of the JackpotCard component showing various content configurations.',
      },
    },
  },
  render: () => {
    const defaultBackground =
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png'
    const defaultImage =
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Default</h4>
              <JackpotCard
                type="Grand"
                currency="SC"
                amount={491098.35}
                backgroundImageSrc={defaultBackground}
                imageSrc={defaultImage}
                lastWonInfo="Last won a month ago"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Background Image</h4>
              <JackpotCard
                type="Minor"
                currency="GC"
                amount={10235.75}
                imageSrc={defaultImage}
                lastWonInfo="Last won yesterday"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Icon Image</h4>
              <JackpotCard
                type="Major"
                currency="SC"
                amount={991234.12}
                backgroundImageSrc={defaultBackground}
                lastWonInfo="Last won 2 weeks ago"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Minimal Content</h4>
              <JackpotCard type="Mini" currency="GC" amount={5000.25} lastWonInfo="Won recently" />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive JackpotCard with all properties customizable.',
      },
    },
  },
  args: {
    type: 'Grand',
    currency: 'SC',
    amount: 491098.35,
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won a month ago',
  },
  render: args => (
    <div
      style={{
        width: '200px',
        minHeight: '200px',
        padding: '16px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <JackpotCard {...args} />
    </div>
  ),
}
