import clsx from 'clsx'
import { ImgIx } from '@components/ImgIx'
import styles from '@modules/jackpots/components/JackpotCard.module.scss'

export interface IJackpotCardProps {
  type: string
  currency: string
  amount: number
  imageSrc?: string
  backgroundImageSrc?: string
  lastWonInfo?: string
}

const JackpotCard = ({ type, currency, amount, imageSrc, backgroundImageSrc, lastWonInfo }: IJackpotCardProps) => {
  return (
    <div className={clsx(styles.jackpotCard, !imageSrc && styles.noIconPadding)}>
      {backgroundImageSrc ? (
        <ImgIx
          src={backgroundImageSrc}
          className={styles.background}
          fill
          alt={'Jackpot Card background'}
          unoptimized
          fallbackText={'Jackpot Card background'}
        />
      ) : null}

      <div className={styles.imageContainer}>
        {imageSrc ? (
          <ImgIx
            className={styles.image}
            src={imageSrc}
            width={82}
            height={82}
            alt={type ? `${type} icon` : 'Jackpot Card icon'}
            unoptimized
            fallbackText={type ? `${type} icon` : 'Jackpot Card icon'}
          />
        ) : null}
      </div>

      <div className={styles.content}>
        {type && currency ? (
          <p className={styles.typeCurrency}>
            {type} {currency}
          </p>
        ) : null}

        {currency && amount !== undefined && amount !== null ? (
          <p className={styles.amount}>
            {currency} {amount.toLocaleString()}
          </p>
        ) : null}

        {lastWonInfo ? <p className={styles.lastWon}>{lastWonInfo}</p> : null}
      </div>
    </div>
  )
}

export default JackpotCard
