import type { FC } from 'react'
import React from 'react'
import { <PERSON><PERSON> } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { ImgIx } from '@components/ImgIx'
import { JackpotCard } from '@modules/jackpots/components'
import type { DynamicallyRenderedJackpotsConfigType } from '@modules/jackpots/JackpotsWidget.schema'
import styles from '@modules/jackpots/JackpotsWidget.module.scss'

export interface IJackpotsWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedJackpotsConfigType
}

// TODO: Replace with RL request once ready
const jackpots = [
  {
    type: 'Grand',
    amount: 491098.35,
    currency: 'SC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won a month ago',
  },
  {
    type: 'Major',
    amount: 125000.75,
    currency: 'GC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won 2 weeks ago',
  },
  {
    type: 'Minor',
    amount: 50000.25,
    currency: 'SC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won yesterday',
  },
  {
    type: 'Mini',
    amount: 10000.0,
    currency: 'GC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won 3 days ago',
  },
  {
    type: 'Mega',
    amount: 750000.5,
    currency: 'SC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won 5 days ago',
  },
  {
    type: 'Super',
    amount: 25000.99,
    currency: 'GC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won 1 week ago',
  },
  {
    type: 'Ultra',
    amount: 999999.99,
    currency: 'SC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won 2 months ago',
  },
  {
    type: 'Daily',
    amount: 5000.0,
    currency: 'GC',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-bg.png',
    imageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/info-card-image.png',
    lastWonInfo: 'Last won today',
  },
]

const JackpotsWidget: FC<IJackpotsWidgetProps> = ({ config }) => {
  if (!config) {
    return null
  }

  const {
    title = 'LuckyOne Jackpot!',
    subtitle = "Don't miss out! Opt in now to join the jackpot.",
    ctaLabel = 'Learn more',
    ctaHref = '#',
    iconSrc,
  } = config

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.imageContainer}>
            {!!iconSrc && (
              <ImgIx src={iconSrc} width={64} height={64} alt="Jackpot icon" unoptimized fallbackText="Jackpot icon" />
            )}
          </div>

          <div>
            {!!title && <h2 className={styles.title}>{title}</h2>}
            {!!subtitle && <p className={styles.subtitle}>{subtitle}</p>}
          </div>
        </div>

        <div>{!!ctaLabel && <Button as="a" href={ctaHref} label={ctaLabel} size="md" color="primary" />}</div>
      </div>

      <div className={styles.scrollContainer}>
        {jackpots.map((jackpot, index) => (
          <div key={`${jackpot.type}-${index}`}>
            <JackpotCard {...jackpot} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default JackpotsWidget
