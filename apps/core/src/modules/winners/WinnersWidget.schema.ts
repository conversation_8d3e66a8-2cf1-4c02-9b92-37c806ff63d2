import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

const WinnerSchema = z.object({
  username: z.string(),
  amount: z.number(),
  thumbnail: z.string(),
  currency: z.string(),
})

export const DynamicallyRenderedWinnersConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.WINNERS),
  meta: z
    .object({
      title: z.string().optional(),
    })
    .optional(),
})

export type DynamicallyRenderedWinnersConfigType = z.infer<typeof DynamicallyRenderedWinnersConfigSchema>['meta']
export type WinnerType = z.infer<typeof WinnerSchema>
