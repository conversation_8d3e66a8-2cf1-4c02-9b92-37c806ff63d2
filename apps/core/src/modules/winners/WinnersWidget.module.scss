@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.container {
  width: 100%;
  background: $color-surface-100;
  border-radius: calculate-rem(8px);
  border: 1px solid $color-surface-200;
  padding: calculate-rem(12px) calculate-rem(19px);
  gap: calculate-rem(16px);
  margin: calculate-rem(8px) 0;
}

.scrollContainer {
  width: 100%;
  display: flex;
  gap: calculate-rem(16px);
  padding: calculate-rem(8px) 0;
  overflow-x: auto;
  scroll-behavior: smooth;
  @include custom-scrollbar(2px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(8px);
    padding: calculate-rem(6px) 0;
  }
}
