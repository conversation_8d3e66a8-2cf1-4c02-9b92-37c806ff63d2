@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.winnerCard {
  max-width: calculate-rem(74px);
  position: relative;
  display: flex;
  padding: 0;
  align-items: center;
  gap: calculate-rem(12px);
  border-radius: $radius-sm;

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(6px);
  }
}

.imageWrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.imageContainer {
  position: relative;
  width: calculate-rem(72px);
  height: calculate-rem(72px);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail {
  width: 100%;
  border-radius: $radius-sm;
  object-fit: cover;
}

.content {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(4px);
  padding: 0;
  min-width: 0;
  max-width: 100%;
  text-align: center;
}

.username {
  padding: 0 calculate-rem(10px);
  font-size: calculate-rem(13px);
  font-weight: 600;
  color: $color-surface-900;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(11px);
    padding: 0 calculate-rem(5px);
  }
}

.winAmount {
  font-size: calculate-rem(16px);
  font-weight: 800;
  color: $color-on-tertiary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: calculate-rem(0.16px);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(10px);
  }
}

.currencyAmount {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: calculate-rem(4px);
  align-items: center;
}

.avatar {
  width: calculate-rem(16px);
  height: calculate-rem(16px);
  background-color: $color-primary;
}

.avatarName {
  color: $color-on-primary;
  font-size: calculate-rem(6.316px);
  font-weight: 900;
  line-height: normal;
}
