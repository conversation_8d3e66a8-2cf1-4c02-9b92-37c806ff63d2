import { Card } from '@components/Card'
import { ImgIx } from '@components/ImgIx'
import { Avatar } from '@heroui/avatar'
import { formatCompact } from '@repo/helpers/formatHelpers'
import styles from '@modules/winners/components/WinnerCard.module.scss'

export interface IWinnerCardProps {
  username: string
  winAmount: number
  gameImageSrc: string
  currency: string
  locale?: string
}

const WinnerCard = ({ username, winAmount, gameImageSrc, currency, locale }: IWinnerCardProps) => {
  return (
    <Card className={styles.winnerCard} aria-label="Winner Card">
      <div className={styles.imageWrapper}>
        <div className={styles.imageContainer}>
          <ImgIx
            src={gameImageSrc}
            alt={`${username}'s winning game thumbnail`}
            width={300}
            className={styles.thumbnail}
            unoptimized
            fallbackText={`${username}'s winning game thumbnail`}
          />
        </div>
      </div>

      <div className={styles.content}>
        <div>
          <p className={styles.username}>{username}</p>
        </div>

        <div className={styles.currencyAmount}>
          <div>
            <Avatar
              size="sm"
              name={currency?.substring(0, 2)}
              classNames={{ base: styles.avatar, name: styles.avatarName }}
            />
          </div>
          <p className={styles.winAmount}>{formatCompact(winAmount, locale)}</p>
        </div>
      </div>
    </Card>
  )
}

export default WinnerCard
