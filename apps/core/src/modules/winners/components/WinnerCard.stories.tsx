import { WinnerCard } from '@modules/winners/components'
import type { Meta, StoryObj } from '@storybook/react'

const meta: Meta<typeof WinnerCard> = {
  title: 'Modules/Winners/WinnerCard',
  component: WinnerCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    username: {
      control: 'text',
      description: 'The username of the winner',
    },
    winAmount: {
      control: 'number',
      description: 'The amount won by the user',
    },
    gameImageSrc: {
      control: 'text',
      description: 'The source URL for the game thumbnail image',
    },
    currency: {
      control: 'text',
      description: 'The currency symbol',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    username: { table: { disable: true } },
    winAmount: { table: { disable: true } },
    gameImageSrc: { table: { disable: true } },
    currency: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different winner card variants showing various username lengths, win amounts, and currencies',
      },
    },
  },
  render: () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Winners Card Variations</h3>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            <WinnerCard
              username="johnwa..."
              winAmount={109500}
              gameImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png"
              currency="$"
            />
            <WinnerCard
              username="bigwin"
              winAmount={2000000}
              gameImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/vampire.png"
              currency="GC"
            />

            <WinnerCard
              username="smallwin"
              winAmount={100}
              gameImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/greek-gods.png"
              currency="GC"
            />
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive winner card with all properties available for customization.',
      },
    },
  },
  args: {
    username: 'johnwa...',
    winAmount: 109500,
    gameImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    currency: '$',
  },
  render: args => (
    <div
      style={{
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <WinnerCard {...args} />
    </div>
  ),
}
