import type { FC } from 'react'
import React from 'react'
import { Card } from '@components/Card'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { GameTile } from '@components/GameTile'
import { WidgetCard, WidgetCardScrollArea } from '@components/WidgetCard/WidgetCard'
import type { DynamicallyRenderedWinnersConfigType } from '@modules/winners/WinnersWidget.schema'
import { formatCompact } from '@repo/helpers/formatHelpers'
import styles from '@modules/winners/WinnersWidget.module.scss'

export interface IWinnersWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedWinnersConfigType
}

// TODO: Replace with API request once ready
const winners = [
  {
    username: 'luckylion',
    amount: 209501,
    currency: 'GC4434',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Jungle Quest', slug: 'jungle-quest' },
  },
  {
    username: 'spintasticpotato',
    amount: 309002,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/vampire.png',
    game: { name: 'Vampire Night', slug: 'vampire-night' },
  },
  {
    username: 'zeusstrikewinner',
    amount: 409003,
    currency: 'GC',
    thumbnail:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/greek-gods.png',
    game: { name: 'Greek Gods', slug: 'greek-gods' },
  },
  {
    username: 'wildnorthstar',
    amount: 20000901,
    currency: 'GC',
    thumbnail:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/wild-north.png',
    game: { name: 'Wild North', slug: 'wild-north' },
  },
  {
    username: 'herculesheropower',
    amount: 20000902,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/hercules.png',
    game: { name: 'Hercules Hero', slug: 'hercules-hero' },
  },
  {
    username: 'gorillaglue',
    amount: 50000903,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Gorilla Gold', slug: 'gorilla-gold' },
  },
  {
    username: 'bananajackpotwinner',
    amount: 60000904,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Banana Jackpot', slug: 'banana-jackpot' },
  },
  {
    username: 'luckyape',
    amount: 70000905,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Lucky Ape', slug: 'lucky-ape' },
  },
  {
    username: 'junglespinadventure',
    amount: 80000906,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Jungle Spin', slug: 'jungle-spin' },
  },
  {
    username: 'apekingofspins',
    amount: 90000907,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Ape King', slug: 'ape-king' },
  },
  {
    username: 'gorillaqueenprize',
    amount: 1000901,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Gorilla Queen', slug: 'gorilla-queen' },
  },
  {
    username: 'spinsalot9000',
    amount: 109004,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Spin Salot', slug: 'spin-salot' },
  },
  {
    username: 'bananahamockchamp',
    amount: 12000908,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
    game: { name: 'Banana Hammock', slug: 'banana-hammock' },
  },
]

const WinnersWidget: FC<IWinnersWidgetProps> = ({ locale }) => {
  return (
    <WidgetCard>
      <WidgetCardScrollArea className={styles.scrollContainer}>
        {winners.map(({ amount, currency, thumbnail, username, game }, i) => (
          <Card key={`WinnerCard-${i}`}>
            <GameTile
              width={72}
              height={72}
              game={{ ...game, meta: { thumbnail: { src: thumbnail } } }}
              gameTileInfo={{
                primaryLabel: username,
                secondaryLabel: formatCompact(amount, locale),
                secondaryIconLabel: currency?.substring(0, 2),
              }}
            />
          </Card>
        ))}
      </WidgetCardScrollArea>
    </WidgetCard>
  )
}

export default WinnersWidget
