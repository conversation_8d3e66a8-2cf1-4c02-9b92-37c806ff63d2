import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { WinnerCard } from '@modules/winners/components'
import type { DynamicallyRenderedWinnersConfigType } from '@modules/winners/WinnersWidget.schema'
import styles from '@modules/winners/WinnersWidget.module.scss'

export interface IWinnersWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedWinnersConfigType
}

// TODO: Replace with API request once ready
const winners = [
  {
    username: 'johnwa dsdsdsc dsd ssdsd sads',
    amount: 109500,
    currency: 'GC4434',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 109000,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/vampire.png',
  },
  {
    username: 'johnwa',
    amount: 109000,
    currency: 'GC',
    thumbnail:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/greek-gods.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/wild-north.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/hercules.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 1000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 109000,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: 20000900,
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
]

const WinnersWidget: FC<IWinnersWidgetProps> = ({ locale }) => {
  return (
    <div className={styles.container}>
      <div className={styles.scrollContainer}>
        {winners.map(({ amount, currency, thumbnail, username }) => (
          <div key={username}>
            <WinnerCard
              username={username}
              winAmount={amount}
              gameImageSrc={thumbnail}
              currency={currency}
              locale={locale}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default WinnersWidget
