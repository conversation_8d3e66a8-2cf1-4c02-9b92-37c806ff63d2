import { Locale } from '@constants/locale'
import { WinnersWidget } from '@modules/winners'
import type { Meta, StoryObj } from '@storybook/react'

const meta: Meta<typeof WinnersWidget> = {
  title: 'Modules/Winners/WinnersWidget',
  component: WinnersWidget,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration object for the winners widget',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different display states of the WinnersWidget showing various configurations and layouts',
      },
    },
  },
  render: () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}> Winners Widget</h3>

          <div style={{ maxWidth: '95%' }}>
            <WinnersWidget locale={Locale.EN} config={{ title: 'Recent Winners' }} />
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive winners widget with customizable configuration properties.',
      },
    },
  },
  args: {
    config: {
      title: 'Recent Winners',
    },
  },
  render: args => (
    <div
      style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <WinnersWidget {...args} />
    </div>
  ),
}
