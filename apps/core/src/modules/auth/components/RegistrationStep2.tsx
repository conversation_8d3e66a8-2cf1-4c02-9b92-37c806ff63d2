'use client'
import React from 'react'
import { <PERSON> } from 'react-hook-form'
import type { UseFormReturn } from 'react-hook-form'
import type { IRegistrationSchema } from '@/modules/auth'
import { Motion } from '@components/Motion/Motion'
import { Alert } from '@heroui/alert'
import { But<PERSON> } from '@heroui/button'
import { Input } from '@heroui/input'
import { Select, SelectItem } from '@heroui/select'
import styles from '@modules/auth/components/RegistrationForm.module.scss'

interface IRegistrationStep2Props {
  form: UseFormReturn<IRegistrationSchema>
  pending: boolean
  registerError: string
  onSubmit?: () => void
  onBack?: () => void
}

// Country codes for phone number
const countryCodes = [
  { key: '+1', label: '+1', flag: '🇺🇸' },
  { key: '+44', label: '+44', flag: '🇬🇧' },
  { key: '+49', label: '+49', flag: '🇩🇪' },
  { key: '+33', label: '+33', flag: '🇫🇷' },
  { key: '+39', label: '+39', flag: '🇮🇹' },
  { key: '+7', label: '+7', flag: '🇷🇺' },
]

// Countries
const countries = [
  { key: 'US', label: 'United States' },
  { key: 'CA', label: 'Canada' },
  { key: 'GB', label: 'United Kingdom' },
  { key: 'DE', label: 'Germany' },
  { key: 'FR', label: 'France' },
  { key: 'IT', label: 'Italy' },
  { key: 'ES', label: 'Spain' },
  { key: 'RU', label: 'Russia' },
]

// Generate days (1-31)
const days = Array.from({ length: 31 }, (_, i) => ({
  key: (i + 1).toString(),
  label: (i + 1).toString().padStart(2, '0'),
}))

// Generate months
const months = [
  { key: '1', label: 'January' },
  { key: '2', label: 'February' },
  { key: '3', label: 'March' },
  { key: '4', label: 'April' },
  { key: '5', label: 'May' },
  { key: '6', label: 'June' },
  { key: '7', label: 'July' },
  { key: '8', label: 'August' },
  { key: '9', label: 'September' },
  { key: '10', label: 'October' },
  { key: '11', label: 'November' },
  { key: '12', label: 'December' },
]

// Generate years (1950-2006 for 18+ validation)
const currentYear = new Date().getFullYear()
const years = Array.from({ length: 57 }, (_, i) => ({
  key: (currentYear - 18 - i).toString(),
  label: (currentYear - 18 - i).toString(),
}))

export const RegistrationStep2: React.FC<IRegistrationStep2Props> = ({
  form,
  pending,
  registerError,
  onSubmit,
  onBack,
}) => {
  return (
    <>
      <Motion layout="size" transition={{ duration: 0.1, ease: 'linear' }}>
        <p className={styles.stepDescription}>
          {"Please enter your details more precisely. We wouldn't collect them, but it's required by law"}
        </p>

        {/* Name fields */}
        <div className={styles.namesSection}>
          <div className={styles.nameFields}>
            <Controller
              name="firstName"
              control={form.control}
              render={({ field }) => (
                <Input
                  label="First name"
                  variant="bordered"
                  {...field}
                  errorMessage={form.formState.errors.firstName?.message}
                  isInvalid={!!form.formState.errors.firstName?.message}
                />
              )}
            />

            <Controller
              name="lastName"
              control={form.control}
              render={({ field }) => (
                <Input
                  label="Last name"
                  variant="bordered"
                  {...field}
                  errorMessage={form.formState.errors.lastName?.message}
                  isInvalid={!!form.formState.errors.lastName?.message}
                />
              )}
            />
          </div>

          {/* Username */}
          <Controller
            name="username"
            control={form.control}
            render={({ field }) => (
              <Input
                label="Enter username"
                variant="bordered"
                {...field}
                errorMessage={form.formState.errors.username?.message}
                isInvalid={!!form.formState.errors.username?.message}
              />
            )}
          />
        </div>
        {/* Date of Birth */}
        <div className={styles.dobSection}>
          <h3 className={styles.sectionTitle}>Date of Birth</h3>
          <div className={styles.dobFields}>
            <Controller
              name="birthDay"
              control={form.control}
              render={({ field }) => (
                <Select
                  label="Day"
                  placeholder="Day"
                  variant="bordered"
                  classNames={{
                    trigger: styles.selectTrigger,
                    value: 'group-data-[has-value=true]:text-primary',
                  }}
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={selection => {
                    const selectedValue = Array.from(selection)[0] as string
                    field.onChange(selectedValue || '')
                  }}
                  errorMessage={form.formState.errors.birthDay?.message}
                  isInvalid={!!form.formState.errors.birthDay?.message}>
                  {days.map(day => (
                    <SelectItem key={day.key}>{day.label}</SelectItem>
                  ))}
                </Select>
              )}
            />

            <Controller
              name="birthMonth"
              control={form.control}
              render={({ field }) => (
                <Select
                  label="Month"
                  placeholder="Month"
                  variant="bordered"
                  classNames={{
                    trigger: styles.selectTrigger,
                  }}
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={selection => {
                    const selectedValue = Array.from(selection)[0] as string
                    field.onChange(selectedValue || '')
                  }}
                  errorMessage={form.formState.errors.birthMonth?.message}
                  isInvalid={!!form.formState.errors.birthMonth?.message}>
                  {months.map(month => (
                    <SelectItem key={month.key}>{month.label}</SelectItem>
                  ))}
                </Select>
              )}
            />

            <Controller
              name="birthYear"
              control={form.control}
              render={({ field }) => (
                <Select
                  label="Year"
                  placeholder="Year"
                  variant="bordered"
                  classNames={{
                    trigger: styles.selectTrigger,
                  }}
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={selection => {
                    const selectedValue = Array.from(selection)[0] as string
                    field.onChange(selectedValue || '')
                  }}
                  errorMessage={form.formState.errors.birthYear?.message}
                  isInvalid={!!form.formState.errors.birthYear?.message}>
                  {years.map(year => (
                    <SelectItem key={year.key}>{year.label}</SelectItem>
                  ))}
                </Select>
              )}
            />
          </div>
        </div>

        {/* Address */}
        <div className={styles.addressSection}>
          <h3 className={styles.sectionTitle}>Address</h3>

          <Controller
            name="streetAddress"
            control={form.control}
            render={({ field }) => (
              <Input
                label="Street Address"
                variant="bordered"
                {...field}
                errorMessage={form.formState.errors.streetAddress?.message}
                isInvalid={!!form.formState.errors.streetAddress?.message}
              />
            )}
          />

          <div className={styles.addressRow}>
            <Controller
              name="city"
              control={form.control}
              render={({ field }) => (
                <Input
                  label="City"
                  variant="bordered"
                  {...field}
                  errorMessage={form.formState.errors.city?.message}
                  isInvalid={!!form.formState.errors.city?.message}
                />
              )}
            />

            <Controller
              name="country"
              control={form.control}
              render={({ field }) => (
                <Select
                  label="Country"
                  placeholder="Country"
                  variant="bordered"
                  classNames={{
                    trigger: styles.selectTrigger,
                  }}
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={selection => {
                    const selectedValue = Array.from(selection)[0] as string
                    field.onChange(selectedValue || '')
                  }}
                  errorMessage={form.formState.errors.country?.message}
                  isInvalid={!!form.formState.errors.country?.message}>
                  {countries.map(country => (
                    <SelectItem key={country.key}>{country.label}</SelectItem>
                  ))}
                </Select>
              )}
            />
          </div>

          <Controller
            name="stateRegion"
            control={form.control}
            render={({ field }) => (
              <Input
                label="State/Region"
                variant="bordered"
                {...field}
                errorMessage={form.formState.errors.stateRegion?.message}
                isInvalid={!!form.formState.errors.stateRegion?.message}
              />
            )}
          />

          <Controller
            name="postalCode"
            control={form.control}
            render={({ field }) => (
              <Input
                label="Postal/ZIP Code"
                variant="bordered"
                {...field}
                errorMessage={form.formState.errors.postalCode?.message}
                isInvalid={!!form.formState.errors.postalCode?.message}
              />
            )}
          />
        </div>

        {/* Phone Number */}
        <div className={styles.phoneSection}>
          <h3 className={styles.sectionTitle}>Phone number</h3>
          <div className={styles.phoneFields}>
            <Controller
              name="phoneCountryCode"
              control={form.control}
              render={({ field }) => (
                <Select
                  label="Code"
                  variant="bordered"
                  classNames={{
                    trigger: styles.phoneCodeSelect,
                  }}
                  selectedKeys={field.value ? [field.value] : []}
                  onSelectionChange={selection => {
                    const selectedValue = Array.from(selection)[0] as string
                    field.onChange(selectedValue || '')
                  }}
                  errorMessage={form.formState.errors.phoneCountryCode?.message}
                  isInvalid={!!form.formState.errors.phoneCountryCode?.message}>
                  {countryCodes.map(code => (
                    <SelectItem key={code.key}>
                      {code.flag} {code.label}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />

            <Controller
              name="phoneNumber"
              control={form.control}
              render={({ field }) => (
                <Input
                  placeholder="000 000 00 00"
                  variant="bordered"
                  {...field}
                  errorMessage={form.formState.errors.phoneNumber?.message}
                  isInvalid={!!form.formState.errors.phoneNumber?.message}
                />
              )}
            />
          </div>
          <p className={styles.phoneNote}>⚠️ It will be verified to ensure its the correct number</p>
        </div>

        {/* Error message */}
        {!!registerError && (
          <div className={styles.errorContainer}>
            <Alert color="danger" variant="solid" description={registerError} title="Registration Error" />
          </div>
        )}

        {/* Complete registration button */}
        <Button type="submit" isLoading={pending} size="lg" onPress={onSubmit} fullWidth>
          Complete registration
        </Button>
      </Motion>
    </>
  )
}
