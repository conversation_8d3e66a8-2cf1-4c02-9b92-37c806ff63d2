import React from 'react'
import { Tab, Tabs } from '@heroui/tabs'
import { LoginForm } from '@modules/auth/components/LoginForm'
import { RegistrationForm } from '@modules/auth/components/RegistrationForm'

export type AuthTab = 'register' | 'login'

export interface IAuthFormProps {
  onSuccess?: VoidFunction
  selected: AuthTab
  onSelectionChange: (item: AuthTab) => void
}

export const AuthForm = ({ selected, onSuccess, onSelectionChange }: IAuthFormProps) => {
  return (
    <Tabs
      destroyInactiveTabPanel={false}
      fullWidth
      aria-label="Tabs form"
      color="primary"
      selectedKey={selected}
      onSelectionChange={item => onSelectionChange?.(item as AuthTab)}
      size="md">
      <Tab key="register" title="Sign up">
        <RegistrationForm onSuccess={onSuccess} />
      </Tab>
      <Tab key="login" title="Sign in">
        <LoginForm onSuccess={onSuccess} />
      </Tab>
    </Tabs>
  )
}
