'use client'
import React, { useState } from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import type { AuthTab } from '@modules/auth/components/AuthForm'
import { AuthForm } from '@modules/auth/components/AuthForm'
import styles from '@modules/auth/AuthScreen.module.scss'

export interface IAuthScreenProps {
  tab: AuthTab
}

export const AuthScreen = ({ tab = 'login' }: IAuthScreenProps) => {
  const [selected, setSelected] = useState<IAuthScreenProps['tab']>(tab)

  return (
    <Card classNames={{ base: styles.card }}>
      <CardHeader className={styles.header}>
        <h1 className={styles.title}>{selected === 'register' ? 'Registration' : 'Login'}</h1>
      </CardHeader>
      <CardBody>
        <AuthForm selected={selected} onSelectionChange={setSelected} />
      </CardBody>
    </Card>
  )
}
