'use client'

import type { JSX } from 'react'
import React from 'react'
import type { MotionProps } from 'motion/react'
import { motion } from 'motion/react'

interface ICustomMotionProps<Tag extends keyof JSX.IntrinsicElements> extends MotionProps {
  type?: Tag
  children: React.ReactNode
  className?: string
}

export const Motion = <Tag extends keyof JSX.IntrinsicElements>({
  type,
  children,
  className,
  ...props
}: ICustomMotionProps<Tag>) => {
  const Component = type ? (motion as any)[type] : motion.div

  return (
    <Component className={className} {...props}>
      {children}
    </Component>
  )
}
