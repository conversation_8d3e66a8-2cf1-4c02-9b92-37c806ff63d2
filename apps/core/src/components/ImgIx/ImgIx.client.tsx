'use client'
import React, { memo, useEffect, useState } from 'react'
import { clsx } from 'clsx'
import type { ImageProps } from 'next/image'
import Image from 'next/image'
import { Blurhash } from 'react-blurhash'
import ImgIxSettings from '@components/ImgIx/ImgIx.settings'
import styles from '@components/ImgIx/ImgIx.module.scss'

interface IImgIxClientProps extends ImageProps {
  blurhash?: string
  blurUrl?: string
  fallbackText?: string
  containerClassName?: string
  asBackground?: boolean
}

/**
 * @description This component is used to render images using ImgIx service on the server side.
 * The ImgIx is used only to get an original asset URL,
 * the resize functionality is handled by the nextjs Image component
 */
const ImgIxClient = memo(
  ({ src, blurhash, blurUrl, fallbackText, containerClassName, asBackground, ...props }: IImgIxClientProps) => {
    const [error, setError] = useState(false)
    const [showBlurPlaceholder, setShowBlurPlaceholder] = useState(!!blurhash || !!blurUrl)
    const [imageLoaded, setImageLoaded] = useState(false)
    const [fallbackImage, setFallbackImage] = useState<string>(ImgIxSettings.IMG_IX_FALLBACK_URL)

    const handleImageLoad = () => {
      setImageLoaded(true)
      setTimeout(() => {
        setShowBlurPlaceholder(false)
      }, 300)
    }

    useEffect(() => {
      if (fallbackText) {
        const params = new URLSearchParams({
          txt: fallbackText,
          'txt-size': '12',
          'txt-color': 'ffffff',
          'txt-align': 'center,middle',
          'txt-fit': 'max',
          'txt-font': 'gillsans',
        })
        const url = `${ImgIxSettings.IMG_IX_FALLBACK_URL}&${params.toString()}`
        setFallbackImage(url)
      }
    }, [error, fallbackText])

    return (
      <div
        className={clsx(
          styles.container,
          styles.blurUrlContainer,
          asBackground && styles.asBackground,
          containerClassName,
        )}
        style={blurUrl && showBlurPlaceholder ? { backgroundImage: `url(${blurUrl})` } : undefined}>
        {!!showBlurPlaceholder && !!blurhash && (
          <div className={clsx(styles.blurhashContainer, styles.visible)}>
            <Blurhash hash={blurhash} width={props.width} height={props.height} className={styles.blurhash} />
          </div>
        )}
        <Image
          src={error ? fallbackImage : src}
          quality={95}
          loading="lazy"
          {...props}
          onError={_ => setError(true)}
          onLoad={handleImageLoad}
          className={clsx(styles.image, imageLoaded && styles.visible, props.className)}
          style={props.style}
        />
      </div>
    )
  },
)

ImgIxClient.displayName = 'ImgIxClient'

export default ImgIxClient
