@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  padding: calculate-rem(8px) calculate-rem(19px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(8px);
    padding: calculate-rem(6px) calculate-rem(12px);
  }
}

.scrollArea {
  display: flex;
  flex-direction: row;
  gap: calculate-rem(16px);
  overflow-x: auto;

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(8px);
  }
}
