import React from 'react'
import type { JSX } from 'react'
import { clsx } from 'clsx'
import type { CardProps } from '@heroui/card'
import { Card, CardBody } from '@heroui/card'
import styles from '@components/WidgetCard/WidgetCard.module.scss'

export const WidgetCard = (props: CardProps) => {
  return <Card shadow="none" radius="sm" {...props} />
}

export const WidgetCardBody = (props: JSX.IntrinsicElements['div']) => {
  return <CardBody className={styles.container} {...props} />
}

export const WidgetCardScrollArea = ({ className, ...props }: JSX.IntrinsicElements['div']) => {
  return <CardBody className={clsx(styles.container, styles.scrollArea, className)} {...props} />
}
