'use client'

import { But<PERSON> } from '@components/Button'
import { DynamicLink } from '@components/DynamicLink'
import { Color } from '@repo/constants/common/colors'
import { Size } from '@repo/constants/common/sizes'
import { useIsMobile } from '@repo/hooks/useMobile'
import { useTheme } from 'next-themes'

export interface IRewardCardCTAProps {
  ctaLabel?: string
  ctaHref?: string
  ctaActionKey?: string
  ctaIcon?: string
  ctaIconDark?: string
  ctaColor: Color
  ctaSize: Size
}

export const BannerCTA = ({ ctaLabel, ctaHref, ctaIconDark, ctaIcon, ctaColor, ctaSize }: IRewardCardCTAProps) => {
  const isMobile = useIsMobile()

  const { resolvedTheme: theme } = useTheme()
  const isDarkMode = theme === 'dark'

  return (
    <Button
      as={ctaHref ? DynamicLink : 'button'}
      {...(ctaHref ? { href: ctaHref } : {})}
      label={ctaLabel}
      color={ctaColor}
      size={isMobile ? 'sm' : ctaSize}
      icon={isDarkMode ? ctaIconDark : ctaIcon}
    />
  )
}
