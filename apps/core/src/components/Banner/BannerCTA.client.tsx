'use client'

import { useTheme } from 'next-themes'
import { Button } from '@components/Button'
import { DynamicLink } from '@components/DynamicLink'
import type { Color } from '@repo/constants/common/colors'
import type { Size } from '@repo/constants/common/sizes'
import { useIsMobile } from '@repo/hooks/useMobile'

export interface IRewardCardCTAProps {
  ctaLabel?: string
  ctaHref?: string
  ctaActionKey?: string
  ctaIcon?: string
  ctaIconDark?: string
  ctaColor: Color
  ctaSize: Size
}

export const BannerCTA = ({ ctaLabel, ctaHref, ctaIconDark, ctaIcon, ctaColor, ctaSize }: IRewardCardCTAProps) => {
  const isMobile = useIsMobile()

  const { resolvedTheme: theme } = useTheme()
  const isDarkMode = theme === 'dark'

  return (
    <Button
      as={ctaHref ? DynamicLink : 'button'}
      {...(ctaHref ? { href: ctaHref } : {})}
      label={ctaLabel}
      color={ctaColor}
      size={isMobile ? 'sm' : ctaSize}
      icon={isDarkMode ? ctaIconDark : ctaIcon}
    />
  )
}
