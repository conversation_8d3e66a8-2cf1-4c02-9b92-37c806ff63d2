import Banner from '@components/Banner/Banner'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof Banner> = {
  title: 'Banner',
  component: Banner,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Banner component displays promotional content with title, subtitle, badge, background image, and CTA buttons. Can be used standalone or within widgets for marketing campaigns and announcements.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    title: 'Welcome to Our Platform',
    subtitle: 'Join thousands of players and start your journey today',
    variant: 'default',
    centered: false,
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Main title text displayed prominently',
    },
    subtitle: {
      control: 'text',
      description: 'Optional subtitle text displayed below the title',
    },
    iconTitleSrc: {
      control: 'text',
      description: 'Optional icon/image URL to display alongside the title',
    },
    backgroundImageSrc: {
      control: 'text',
      description: 'Optional background image URL for the banner',
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'padded'],
      description: 'Banner layout variant - default or padded',
    },
    centered: {
      control: 'boolean',
      description: 'Whether to center the banner horizontally',
    },
    maxWidth: {
      control: 'text',
      description: 'Maximum width constraint (CSS value)',
    },
    maxHeight: {
      control: 'text',
      description: 'Maximum height constraint (CSS value)',
    },
    badge: {
      control: 'object',
      description: 'Optional badge with label and icon',
    },
    ctaButtons: {
      control: 'object',
      description: 'Array of CTA buttons with various properties',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          margin: '0 auto',
          padding: '20px',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    title: { table: { disable: true } },
    subtitle: { table: { disable: true } },
    iconTitleSrc: { table: { disable: true } },
    backgroundImageSrc: { table: { disable: true } },
    variant: { table: { disable: true } },
    centered: { table: { disable: true } },
    maxWidth: { table: { disable: true } },
    maxHeight: { table: { disable: true } },
    badge: { table: { disable: true } },
    ctaButtons: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All banner variants showing different layouts, content types, and styling options',
      },
    },
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Basic Variants</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}> Title Only</h4>
            <Banner
              title="Simple Banner Title"
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Title & Subtitle</h4>
            <Banner
              title="Welcome to Our Platform"
              subtitle="Join thousands of players and start your journey today"
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>With Badges</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Badge with Icon</h4>
            <Banner
              title="New Feature Available"
              subtitle="Check out our latest gaming addition"
              badge={{
                label: 'NEW',
                iconSrc:
                  'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
              }}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Badge Text Only</h4>
            <Banner
              title="Limited Time Promotion"
              subtitle="Hurry up, offer expires soon!"
              badge={{
                label: 'HOT DEAL',
              }}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>With Icon Title</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Logo Integration </h4>
            <Banner
              title="PURCHASE NOW AND WIN BIG"
              iconTitleSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner-icon.png"
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
              ctaButtons={[
                {
                  label: 'Get Started',
                  href: '#',
                  color: 'primary',
                  size: 'md',
                },
              ]}
            />
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Single CTA Button</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Primary Button</h4>
            <Banner
              title="Join Our Community"
              subtitle="Start your gaming adventure today"
              ctaButtons={[
                {
                  label: 'Get Started',
                  href: '#',
                  color: 'primary',
                  size: 'md',
                },
              ]}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Secondary Button</h4>
            <Banner
              title="Learn More About Us"
              subtitle="Discover what makes us different"
              ctaButtons={[
                {
                  label: 'Learn More',
                  href: '#',
                  color: 'secondary',
                  size: 'md',
                },
              ]}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Button with Icon</h4>
            <Banner
              title="Download Our App"
              subtitle="Get the best mobile gaming experience"
              ctaButtons={[
                {
                  label: 'Download',
                  href: '#',
                  color: 'primary',
                  size: 'md',
                  icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                  icon_dark:
                    'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
                },
              ]}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Multiple CTA Buttons</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Primary & Secondary</h4>
            <Banner
              title="Choose Your Path"
              subtitle="Multiple ways to get started with our platform"
              ctaButtons={[
                {
                  label: 'Sign Up',
                  href: '#',
                  color: 'primary',
                  size: 'md',
                },
                {
                  label: 'Learn More',
                  href: '#',
                  color: 'secondary',
                  size: 'md',
                },
              ]}
              variant="default"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
            />
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Different Sizes</h4>
            <Banner
              title="Flexible Options"
              subtitle="Choose the action that suits you best"
              backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
              ctaButtons={[
                {
                  label: 'Primary Action',
                  href: '#',
                  color: 'primary',
                  size: 'lg',
                },
                {
                  label: 'Secondary',
                  href: '#',
                  color: 'secondary',
                  size: 'sm',
                },
                {
                  label: 'Tertiary',
                  href: '#',
                  color: 'tertiary',
                  size: 'sm',
                },
                {
                  href: '#',
                  color: 'primary',
                  size: 'lg',
                  icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                },
                {
                  href: '#',
                  color: 'secondary',
                  size: 'md',
                  icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                },
                {
                  href: '#',
                  color: 'tertiary',
                  size: 'sm',
                  icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                },
              ]}
              variant="default"
            />
          </div>
        </div>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Full-Featured Examples</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Gaming Promotion (Padded) + Icon only button
              </h4>
              <Banner
                title="Epic Tournament Starting Soon"
                subtitle="Join the biggest gaming event of the year and win amazing prizes"
                backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
                badge={{
                  label: 'LIVE EVENT',
                  iconSrc:
                    'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
                }}
                ctaButtons={[
                  {
                    label: 'Join Tournament',
                    href: '#',
                    color: 'primary',
                    size: 'lg',
                    icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                  },
                  {
                    href: '#',
                    color: 'secondary',
                    size: 'lg',
                    icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                  },
                ]}
                variant="padded"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Welcome Bonus (Default - Not Padded)
              </h4>
              <Banner
                title="Welcome Bonus Package"
                subtitle="Get up to $1000 bonus on your first deposit plus 100 free spins"
                backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
                iconTitleSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner-icon.png"
                badge={{
                  label: 'NEW PLAYER',
                }}
                ctaButtons={[
                  {
                    label: 'Claim Bonus',
                    href: '#',
                    color: 'primary',
                    size: 'lg',
                  },
                ]}
                variant="default"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                VIP Program (Padded - Tertiary button)
              </h4>
              <Banner
                title="Exclusive VIP Experience"
                subtitle="Unlock premium benefits, personal account manager, and exclusive rewards"
                backgroundImageSrc="https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png"
                badge={{
                  label: 'PREMIUM',
                  iconSrc:
                    'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
                }}
                ctaButtons={[
                  {
                    label: 'Join VIP',
                    href: '#',
                    color: 'tertiary',
                    size: 'lg',
                    icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
                  },
                  {
                    label: 'Learn More',
                    href: '#',
                    color: 'secondary',
                    size: 'md',
                  },
                ]}
                variant="padded"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive Banner with all properties available for customization',
      },
    },
  },
  args: {
    title: 'Customize This Banner',
    subtitle: 'Use the controls below to modify all banner properties',
    backgroundImageSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner.png',
    iconTitleSrc:
      'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/backgrounds/banner-icon.png',
    badge: {
      label: 'DEMO',
      iconSrc: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
    },
    ctaButtons: [
      {
        label: 'Primary Action',
        href: '#',
        color: 'primary',
        size: 'md',
        icon: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift.svg',
        icon_dark: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/icons/gift_dark.svg',
      },
      {
        label: 'Secondary',
        href: '#',
        color: 'secondary',
        size: 'md',
      },
    ],
    variant: 'default',
    centered: false,
    maxWidth: '',
    maxHeight: '',
  },
  render: args => (
    <div
      style={{
        width: '100%',
        padding: '2rem',
        display: 'flex',
        justifyContent: 'center',
      }}>
      <Banner {...args} />
    </div>
  ),
}
