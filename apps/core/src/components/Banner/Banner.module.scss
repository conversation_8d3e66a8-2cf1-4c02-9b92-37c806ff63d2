@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  border-radius: calculate-rem(8px);
  position: relative;
  gap: calculate-rem(20px);
}

.default {
  padding: calculate-rem(22px) calculate-rem(28px);
  @media (max-width: $breakpoint-mobile) {
    padding: calculate-rem(16px) calculate-rem(18px);
  }
}

.padded {
  padding: calculate-rem(88px) calculate-rem(90px);
  @media (max-width: $breakpoint-mobile) {
    padding: calculate-rem(16px) calculate-rem(18px);
  }
}

.centered {
  margin: 0 auto;
}

.badge {
  width: fit-content;
  background-color: $color-background;
  display: flex;
  align-items: center;
  padding: calculate-rem(4px) calculate-rem(6px) calculate-rem(4px) calculate-rem(4px);
  gap: calculate-rem(8px);
  border-radius: calculate-rem(6px);
  z-index: 1;
}

.badgeIconContainer {
  width: calculate-rem(24px);
  height: calculate-rem(24px);
  background-color: $color-primary;
  padding: calculate-rem(4px);
  border-radius: calculate-rem(4px);
}

.badgeIcon {
  width: calculate-rem(16px);
  height: calculate-rem(16px);
  position: relative;
}

.badgeTitle {
  font-size: calculate-rem(15px);
  font-weight: 600;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(12px);
    font-weight: 500;
  }
}

.content {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(18px);
}

.titles {
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(18px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(8px);
  }
}

.title {
  max-width: 50%;
  color: $color-on-tertiary;
  font-size: calculate-rem(22px);
  line-height: calculate-rem(22px);
  letter-spacing: calculate-rem(0.22px);
  font-weight: 800;
  @include line-clamp(3);

  @media (max-width: 768px) {
    max-width: 50%;
    line-height: calculate-rem(20px);
    font-size: calculate-rem(16px);
    word-wrap: break-word;
  }
}

.subTitle {
  max-width: 45%;
  color: $color-surface-900;
  font-size: calculate-rem(14px);
  font-weight: 600;
  word-wrap: break-word;
  @include line-clamp(3);

  @media (max-width: 768px) {
    max-width: 40%;
    font-size: calculate-rem(14px);
    font-weight: 500;
    word-wrap: break-word;
    @include line-clamp(2);
  }
}

.imageContainer {
  position: relative;
  display: flex;
  align-items: center;
  max-width: calculate-rem(159px);
  height: calculate-rem(22px);
  object-fit: cover;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-bottom: calculate-rem(16px);
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
  border-radius: calculate-rem(8px);
}

.ctaButtons {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: calculate-rem(8px);
}
