import type { FC } from 'react'
import styles from '@components/Banner/Banner.module.scss'
import { ImgIx } from '@components/ImgIx'
import { BannerCTA } from '@components/Banner/BannerCTA.client'
import { Color } from '@repo/constants/common/colors'
import { Size } from '@repo/constants/common/sizes'
import clsx from 'clsx'

interface IBannerProps {
  title: string
  iconTitleSrc?: string
  subtitle?: string
  backgroundImageSrc?: string
  badge?: {
    label: string
    iconSrc?: string
  }
  ctaButtons?: Array<{
    label?: string
    href?: string
    icon?: string
    icon_dark?: string
    color?: Color
    size?: Size
  }>
  variant?: 'default' | 'padded'
  maxWidth?: string | number
  maxHeight?: string | number
  centered?: boolean
}

const Banner: FC<IBannerProps> = props => {
  const {
    title,
    iconTitleSrc,
    backgroundImageSrc,
    ctaButtons = [],
    badge: { label: badgeLabel, iconSrc: badgeIconSrc } = {},
    subtitle,
    variant = 'default',
    maxWidth,
    maxHeight,
    centered,
  } = props

  return (
    <div
      className={clsx(styles.wrapper, styles[variant], centered && styles.centered)}
      style={{
        maxWidth,
        maxHeight,
      }}>
      {!!backgroundImageSrc && (
        <ImgIx src={backgroundImageSrc} className={styles.image} quality={100} fill alt={title} />
      )}

      <div className={styles.content}>
        {!!badgeIconSrc && !!badgeLabel && (
          <div className={styles.badge}>
            <div className={styles.badgeIconContainer}>
              <div className={styles.badgeIcon}>
                <ImgIx src={badgeIconSrc} className={styles.image} quality={100} width={16} height={16} alt={title} />
              </div>
            </div>
            <h3 className={styles.badgeTitle}>{badgeLabel}</h3>
          </div>
        )}

        {!!iconTitleSrc && (
          <div className={styles.imageContainer}>
            <ImgIx src={iconTitleSrc} alt={title} fill />
          </div>
        )}

        <div className={styles.titles}>
          {!!title && <h3 className={styles.title}>{title}</h3>}

          {subtitle && <h4 className={styles.subTitle}>{subtitle}</h4>}
        </div>
      </div>

      <div className={styles.ctaButtons}>
        {ctaButtons.length &&
          ctaButtons.map(({ color = 'primary', href, label, icon, icon_dark, size = 'sm' }) => {
            return (
              <div>
                <BannerCTA
                  ctaColor={color}
                  ctaLabel={label}
                  ctaSize={size}
                  ctaHref={href}
                  ctaIcon={icon}
                  ctaIconDark={icon_dark}
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}

export default Banner
