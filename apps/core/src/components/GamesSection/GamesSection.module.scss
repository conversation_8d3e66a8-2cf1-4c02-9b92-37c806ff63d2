@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

$mobile-padding: 0 calculate-rem(10px);

.container {
  display: flex;
  flex-direction: column;
}

.titleContainer {
  height: calculate-rem(40px);
  color: $color-primary;
  display: flex;
  align-items: center;
  @media (max-width: 767px) {
    padding: $mobile-padding;
  }
}

.title {
  font-size: calculate-rem(15px);
}

.games {
  display: grid;
  gap: calculate-rem(10px);
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  justify-content: start;
  grid-template-rows: repeat(var(--desktop-row-count, 1), minmax($game-tile-default-height, auto));
  grid-auto-flow: column;

  @media (max-width: 767px) {
    grid-template-rows: repeat(var(--mobile-row-count, 1), minmax($game-tile-default-height, auto));
    padding: $mobile-padding;
  }
}
