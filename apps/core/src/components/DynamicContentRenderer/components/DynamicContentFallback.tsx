import type { FC } from 'react'
import { Card } from '@heroui/card'
import styles from '@components/DynamicContentRenderer/components/DynamicContentFallback.module.scss'

interface IDynamicContentFallbackProps {
  message?: string
}

export const DynamicContentFallback: FC<IDynamicContentFallbackProps> = ({ message }) => {
  return (
    <Card isDisabled className={styles.container} role="presentation">
      <p className={styles.headline}>Oops!</p>
      <p className={styles.subline}>{message || "Something went sideways. We can't render this content."}</p>
    </Card>
  )
}
