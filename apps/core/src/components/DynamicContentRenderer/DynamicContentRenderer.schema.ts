import { z } from 'zod'
import {
  AuthTypeEnum,
  DynamicallyRenderedContainer,
  LayoutEnum,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DeviceTypeEnum } from '@repo/helpers/deviceHelpers'

const LayoutEnumZ = z.nativeEnum(LayoutEnum)

export const DynamicallyRenderedContentHideAuthSchema = z.nativeEnum(AuthTypeEnum).optional()
export const DynamicallyRenderedContentHideDeviceSchema = z.array(z.nativeEnum(DeviceTypeEnum)).min(1).optional()
export const DynamicallyRenderedContentHideSchema = z.object({
  auth: DynamicallyRenderedContentHideAuthSchema,
  devices: DynamicallyRenderedContentHideDeviceSchema,
})
export type DynamicallyRenderedContentHideType = z.infer<typeof DynamicallyRenderedContentHideSchema>

export const DynamicallyRenderedContentLayoutSchema = z.object({
  direction: LayoutEnumZ.optional(),
  maxWidth: z.string().optional(),
  minWidth: z.string().optional(),
  maxHeight: z.string().optional(),
  minHeight: z.string().optional(),
  flex: z.number().optional(),
})

export const DynamicallyRenderedContentBaseConfigSchema = z.object({
  id: z.string().optional(),
  component: z.string(),
  hide_for: DynamicallyRenderedContentHideSchema.optional(),
  layoutConfig: DynamicallyRenderedContentLayoutSchema.optional(),
})

export const DynamicallyRenderedContainerConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedContainer.CONTAINER),
  title: z.string().optional(), // do we need this?
  layoutConfig: DynamicallyRenderedContentLayoutSchema.optional(),
  meta: z.object({
    // may be we can omit the meta wrapper
    layout: LayoutEnumZ.optional(), // for backward compatibility
    items: z.array(z.any()).min(1),
  }),
})
export type DynamicallyRenderedContainerConfigType = z.infer<typeof DynamicallyRenderedContainerConfigSchema>
