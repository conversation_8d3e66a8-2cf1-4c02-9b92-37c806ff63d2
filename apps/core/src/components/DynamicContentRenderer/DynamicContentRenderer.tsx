import type { FC } from 'react'
import React from 'react'
import clsx from 'clsx'
import { omit } from 'lodash-es'
import { DynamicContentFallback } from '@components/DynamicContentRenderer/components/DynamicContentFallback'
import DynamicContentRendererConsts from '@components/DynamicContentRenderer/DynamicContentRenderer.consts'
import type { DynamicallyRenderedContentType } from '@components/DynamicContentRenderer/DynamicContentRenderer.derived-schema'
import {
  DynamicallyRenderedContainerConfigSchema,
  DynamicallyRenderedContentBaseConfigSchema,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import type { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { checkDynamicRenderEligibility } from '@components/DynamicContentRenderer/utils/checkDynamicRenderEligibility'
import { componentRegistry } from '@components/DynamicContentRenderer/utils/componentRegistry'
import type { Locale } from '@constants/locale'

interface IRenderDynamicContentRecursivelyProps {
  contentConfig: DynamicallyRenderedContentType
  locale: Locale
  requestParams?: Record<string, string>
}

const renderDynamicContentRecursively = async ({
  contentConfig,
  locale,
  requestParams,
}: IRenderDynamicContentRecursivelyProps) => {
  const { error: baseValidationError } = DynamicallyRenderedContentBaseConfigSchema.safeParse(contentConfig)

  if (baseValidationError) {
    console.error(`[DynamicallyRenderedContent] Base validation failed for ${contentConfig.id}`, {
      contentConfig,
      error: baseValidationError,
    })
    return <DynamicContentFallback message={baseValidationError.message} />
  }

  const shouldRender = await checkDynamicRenderEligibility(contentConfig.hide_for)

  if (!shouldRender) {
    return null
  }

  switch (contentConfig.component) {
    case 'container': {
      const { error: containerConfigError } = DynamicallyRenderedContainerConfigSchema.safeParse(contentConfig)

      if (containerConfigError) {
        console.error(
          `[DynamicallyRenderedContent] Invalid container ${contentConfig.id} configuration:`,
          containerConfigError,
        )
        return <DynamicContentFallback message={containerConfigError.issues?.[0]?.message} />
      }

      const items = contentConfig.meta?.items

      const content = await Promise.all(
        items.map(async (item, index) => (
          <React.Fragment key={'container-' + index}>
            {await renderDynamicContentRecursively({ contentConfig: item, locale, requestParams })}
          </React.Fragment>
        )),
      )

      const layoutConfig = contentConfig.layoutConfig
      const direction = layoutConfig?.direction || contentConfig.meta?.layout || 'vertical'

      return (
        <div
          className={clsx(
            `flex  gap-[8px] md:gap-[16px] ${direction === 'vertical' ? 'flex-col' : 'flex-row flex-wrap'}`,
          )}
          style={omit(layoutConfig, ['direction'])}>
          {content}
        </div>
      )
    }

    default: {
      const Component = componentRegistry[contentConfig.component as keyof typeof componentRegistry]

      if (!Component) {
        console.warn(`[DynamicallyRenderedContent] Component "${contentConfig.component}" not found in registry`)
        return <DynamicContentFallback />
      }

      const WidgetSchemaByComponent =
        DynamicContentRendererConsts.COMPONENT_TO_SCHEMA_MAP[contentConfig.component as DynamicallyRenderedWidget]
      if (!WidgetSchemaByComponent) {
        console.error(`[DynamicallyRenderedContent] Unsupported "${contentConfig.component}" component`)
        return <DynamicContentFallback />
      }
      const { error: widgetConfigError } = WidgetSchemaByComponent.safeParse(contentConfig)
      if (widgetConfigError) {
        console.error('[DynamicallyRenderedContent] Invalid widget configuration:', widgetConfigError)
        return <DynamicContentFallback message={widgetConfigError.issues?.[0]?.message} />
      }

      const layoutConfig = contentConfig.layoutConfig

      return (
        <div
          className="flex-1 overflow-x-auto animate-in fade-in duration-300 ease-in-out"
          style={omit(layoutConfig, ['direction'])}>
          <Component
            locale={locale}
            requestParams={requestParams}
            config={'meta' in contentConfig ? contentConfig.meta : ({} as any)}
          />
        </div>
      )
    }
  }
}

interface IDynamicallyRenderedContentProps extends Omit<IRenderDynamicContentRecursivelyProps, 'contentConfig'> {
  config: IRenderDynamicContentRecursivelyProps['contentConfig']
}

const DynamicContentRenderer: FC<IDynamicallyRenderedContentProps> = async ({ config, locale, requestParams }) => {
  return await renderDynamicContentRecursively({ contentConfig: config, locale, requestParams })
}

export default DynamicContentRenderer
