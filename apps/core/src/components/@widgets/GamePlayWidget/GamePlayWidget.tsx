import type { FC } from 'react'
import clsx from 'clsx'
import { rl<PERSON><PERSON>ch<PERSON><PERSON> } from '@/network/server-utils/rl/RLFetchApi'
import { getAvailableGamesMapBySlug } from '@/network/server-utils/s3/getters/games'
import { authService } from '@/services/auth.service'
import { getLicenseByLocale } from '@/utils/locale'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { Locale } from '@constants/locale'
import { Alert } from '@heroui/alert'
import styles from '@widgets/GamePlayWidget/GamePlayWidget.module.scss'

export const GamePlayWidget: FC<IDynamicallyRenderedContentProps> = async ({ requestParams, locale }) => {
  const { gameSlug } = requestParams || {}

  if (!gameSlug || !locale) {
    console.error('[GamePlayWidget] Missing gameSlug or locale in requestParams')
    return (
      <Alert color="danger" variant="solid">
        Missing params
      </Alert>
    )
  }

  const gamesMap = await getAvailableGamesMapBySlug(locale as Locale)
  const game = gamesMap[gameSlug]

  if (!game) {
    console.error(`[GamePlayWidget] Game with slug "${gameSlug}" not found for locale "${locale}"`)
    return (
      <Alert color="danger" variant="solid">
        Game not found
      </Alert>
    )
  }

  const session = await authService.getUserInfo()
  const userLegislation = undefined // TODO: Pass proper value when we have proper user info (session.legislation?)
  const userCountryCode = undefined // session.registrationCountry?

  const rlGame = await rlFetchApi.getRlGame({
    isLoggedIn: !!session,
    locale,
    slug: game.slug,
    userCountryCode,
    license: userLegislation || getLicenseByLocale(locale),
  })

  console.log({ rlGame })

  return (
    <div className={clsx(styles.container)}>
      <div className={styles.gameContainer}>
        <iframe src={rlGame?.launchUrl} width="100%" height="100%" />
      </div>
    </div>
  )
}

export default GamePlayWidget
