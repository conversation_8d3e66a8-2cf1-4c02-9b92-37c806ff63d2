import type { FC } from 'react'
import clsx from 'clsx'
import { getAvailableGamesMapBySlug } from '@/network/server-utils/s3/getters/games'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { Locale } from '@constants/locale'
import { Alert } from '@heroui/alert'
import styles from '@widgets/GamePlayWidget/GamePlayWidget.module.scss'

export const GamePlayWidget: FC<IDynamicallyRenderedContentProps> = async ({ requestParams, locale }) => {
  const { gameSlug } = requestParams || {}

  if (!gameSlug || !locale) {
    console.error('[GamePlayWidget] Missing gameSlug or locale in requestParams')
    return (
      <Alert color="danger" variant="solid">
        Missing params
      </Alert>
    )
  }

  const gamesMap = await getAvailableGamesMapBySlug(locale as Locale)
  const game = gamesMap[gameSlug]

  if (!game) {
    console.error(`[GamePlayWidget] Game with slug "${gameSlug}" not found for locale "${locale}"`)
    return (
      <Alert color="danger" variant="solid">
        Game not found
      </Alert>
    )
  }

  //TODO get gameLaunchUrl from the RL, implement getRlGame by url: `/games/pragmatic/${!reg ? 'demo/' : ''}${slug}`,

  return (
    <div className={clsx(styles.container)}>
      <div className={styles.gameContainer}>
        <iframe
          src="https://demogamesfree.pragmaticplay.net/gs2c/openGame.do?gameSymbol=big_bonanza&lang=en&stylename=lckn_luckyonegco&isGameUrlApiCalled=true&userId=DEMO"
          title="Game Play"
          width={'100%'}
          height={'100%'}
        />
      </div>
    </div>
  )
}

export default GamePlayWidget
