import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedTextConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.TEXT),
  meta: z.object({
    content: z.string(),
    alignment: z.enum(['left', 'center', 'right', 'justify']).optional().default('left'),
    color: z
      .enum(['primary', 'secondary', 'tertiary', 'success', 'error', 'surface', 'default'])
      .optional()
      .default('default'),
    size: z.enum(['small', 'medium', 'large', 'xl']).optional().default('medium'),
    weight: z.enum(['light', 'normal', 'medium', 'semibold', 'bold']).optional().default('normal'),
    fontFamily: z.string().optional(),
    lineHeight: z.string().optional(),
    className: z.string().optional(),
  }),
})

export type DynamicallyRenderedTextConfigType = z.infer<typeof DynamicallyRenderedTextConfigSchema>['meta']
