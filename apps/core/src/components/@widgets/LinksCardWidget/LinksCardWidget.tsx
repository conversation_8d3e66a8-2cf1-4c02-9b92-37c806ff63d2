import type { FC } from 'react'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@components/Card'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { ImgIx } from '@components/ImgIx'
import type { DynamicallyRenderedLinksCardConfigType } from '@widgets/LinksCardWidget/LinksCardWidget.schema'
import LinksCardWidgetCTA from '@widgets/LinksCardWidget/LinksCardWidgetCTA'
import styles from '@widgets/LinksCardWidget/LinksCardWidget.module.scss'

export interface ILinksCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedLinksCardConfigType
}
const LinksCardWidget: FC<ILinksCardWidgetProps> = ({ config, locale }) => {
  const { headline, backgroundUrl, items } = config

  return (
    <Card className={styles.socialMediaCard}>
      {backgroundUrl ? (
        <ImgIx
          src={backgroundUrl}
          asBackground
          placeholder="url"
          className={styles.backgroundImage}
          width={300}
          unoptimized
          alt="card-background"
          style={{
            objectFit: 'cover',
          }}
        />
      ) : null}

      <CardHeader className={styles.header}>
        <CardTitle as="h2" className={styles.title}>
          {headline}
        </CardTitle>
      </CardHeader>

      <CardContent className={styles.content}>
        {items.map(({ icon, icon_dark, alt_text, href }) => (
          <LinksCardWidgetCTA key={alt_text} icon={icon} icon_dark={icon_dark} alt_text={alt_text} href={href} />
        ))}
      </CardContent>
    </Card>
  )
}

export default LinksCardWidget
