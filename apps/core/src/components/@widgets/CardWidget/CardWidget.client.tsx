'use client'
import { use<PERSON><PERSON>back, useMemo, type FC } from 'react'
import { clsx } from 'clsx'
import { useLocale } from '@/hooks/locale'
import { useAppDispatch } from '@/store/hooks'
import { rhinolayerApi, useGetActiveBonusesForEcrQuery } from '@/store/rhinoLayer/rhinoLayer.api'
import { getLicenseByLocale } from '@/utils/locale'
import { Button } from '@components/Button'
import ImgIxClient from '@components/ImgIx/ImgIx.client'
import { getImgIxBlurUrl } from '@components/ImgIx/ImgIx.utils'
import TimerClient from '@components/Timer/Timer.client'
import type { IBonus } from '@repo/types/api/rl/bonus'
import type { DynamicallyRenderedCardConfigType } from '@widgets/CardWidget/CardWidget.schema'
import styles from '@widgets/CardWidget/CardWidget.module.scss'

interface ICardWidgetClientProps {
  config: DynamicallyRenderedCardConfigType
  initialBonus?: IBonus
}

export const CardWidgetClient: FC<ICardWidgetClientProps> = ({ config, initialBonus }) => {
  const locale = useLocale()
  const license = getLicenseByLocale(locale)
  const hasIllustration = true // TODO: Refactor logic
  const id = config.bonusId
  const bonus = initialBonus || null
  //   const { data: _bonus, isLoading } = useGetBonusQuery({ id, locale, license })
  //   const bonus = useMemo(() => (isLoading ? initialBonus : _bonus), [_bonus, initialBonus, isLoading])
  const ecrId = 123456789 // TODO: Replace with actual ECR ID
  const { data: userBonuses, isLoading: areUserBonusesLoading } = useGetActiveBonusesForEcrQuery(
    {
      ecrId,
      locale,
      license,
    },
    { skip: !ecrId || !bonus || true }, // TODO RL does not support bonuses yet
  )
  const isBonusClaimed = useMemo(
    () => (areUserBonusesLoading ? false : userBonuses?.some((b: IBonus) => b.id === id)),
    [areUserBonusesLoading, userBonuses, id],
  )
  const dispatch = useAppDispatch()
  const claimBonus = async (id: number) => {
    console.log('Claiming bonus:', id)
  }
  const invalidateUserBonusesData = useCallback(() => {
    dispatch(rhinolayerApi.util.invalidateTags(['USER_BONUSES']))
  }, [dispatch])
  const handleClaimClick = useCallback(
    async (bonusClaimId: number) => {
      try {
        await claimBonus(bonusClaimId)
        invalidateUserBonusesData()
      } catch (error) {
        console.error('Error claiming bonus:', error)
      }
    },
    [invalidateUserBonusesData],
  )
  const invalidateBonusData = useCallback(() => {
    // NOTE: Expect not to get expired bonuses in the response
    dispatch(rhinolayerApi.util.invalidateTags([{ type: 'BONUS', id }]))
  }, [dispatch, id])

  if (!bonus || !config) {
    return null
  }

  return (
    <div className={styles.container}>
      <ImgIxClient
        src={config.backgroundUrl + `?dpr=2&w=${240}&q=95`}
        asBackground
        blurUrl={getImgIxBlurUrl(config.backgroundUrl)}
        className={styles.featuredOfferBackground}
        fill
        unoptimized
        alt="card-background"
        style={{
          objectFit: 'cover',
        }}
      />
      <div
        className={clsx(
          styles.content,
          hasIllustration ? styles.featuredOffer : styles.featuredOfferWithoutIllustration,
        )}>
        <div className={styles.textContent}>
          <h3 className={styles.headline}>{config.headline}</h3>
          <p className={styles.subline}>{config.subline}</p>
        </div>
        {!isBonusClaimed ? (
          <Button label={config.ctaTitle} onPress={() => handleClaimClick(id)} color="secondary" />
        ) : (
          <TimerClient startTime={new Date()} endTime={new Date(bonus?.expiresAt)} onExpire={invalidateBonusData} />
        )}
      </div>
    </div>
  )
}
