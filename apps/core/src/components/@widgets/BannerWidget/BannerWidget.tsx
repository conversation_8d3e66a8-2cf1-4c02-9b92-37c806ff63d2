import type { FC } from 'react'
import React from 'react'
import Banner from '@components/Banner/Banner'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedBannerConfigType } from '@widgets/BannerWidget/BannerWidget.schema'

export interface IBannerWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedBannerConfigType
}

const BannerWidget: FC<IBannerWidgetProps> = ({ config }) => {
  const ctaButtons = [
    ...(config.primaryCtas?.map(cta => ({
      label: cta.label,
      href: cta.href,
      icon: cta.icon,
      icon_dark: cta.icon_dark,
      color: cta.color,
      size: cta.size,
    })) || []),
    ...(config.secondaryCtas?.map(cta => ({
      label: cta.label,
      href: cta.href,
      icon: cta.icon,
      icon_dark: cta.icon_dark,
      color: cta.color,
      size: cta.size,
    })) || []),
  ]

  return (
    <Banner
      title={config.headline || ''}
      subtitle={config.subline}
      backgroundImageSrc={config.backgroundUrl}
      iconTitleSrc={config.iconTitleSrc}
      badge={
        config.labelText
          ? {
              label: config.labelText,
              iconSrc: config.labelIcon,
            }
          : undefined
      }
      ctaButtons={ctaButtons.length > 0 ? ctaButtons : undefined}
      variant={config.variant}
      centered={config.centered}
      maxWidth={config.maxWidth}
      maxHeight={config.maxHeight}
    />
  )
}

export default BannerWidget
