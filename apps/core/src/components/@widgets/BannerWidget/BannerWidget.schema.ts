import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

const CtaSchema = z
  .object({
    label: z.string().optional(),
    icon: z.string().optional(),
    icon_dark: z.string().optional(),
    href: z.string(),
    color: z.enum(['primary', 'secondary', 'tertiary']).optional(),
    size: z.enum(['xs', 'sm', 'md', 'lg', 'xl']).optional(),
  })
  .refine(data => data.label || data.icon, {
    message: "Either 'label' or 'icon' must be provided",
  })

export const DynamicallyRenderedBannerConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.BANNER),
  meta: z.object({
    headline: z.string().optional(),
    subline: z.string().optional(),
    backgroundUrl: z.string().optional(),
    iconTitleSrc: z.string().optional(),
    labelText: z.string().optional(),
    labelIcon: z.string().optional(),
    primaryCtas: z.array(CtaSchema).optional(),
    secondaryCtas: z.array(CtaSchema).optional(),
    variant: z.enum(['default', 'padded']).optional(),
    centered: z.boolean().optional(),
    maxWidth: z.string().optional(),
    maxHeight: z.string().optional(),
  }),
})

export type DynamicallyRenderedBannerConfigType = z.infer<typeof DynamicallyRenderedBannerConfigSchema>['meta']
