@use './Button.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.primary {
  background: $type-primary-default-background;
  color: $type-primary-default-text;
}

.primary:hover:not(:disabled) {
  background: $type-primary-hover-background;
  color: $type-primary-hover-text;
}

/* Only primary disabled button tokens are defined,
currently on Figma all buttons have the same design for disabled state */
.primary:disabled,
.secondary:disabled,
.tertiary:disabled {
  background: $type-primary-disabled-background;
  color: $type-primary-disabled-text;
}

.secondary {
  background: $type-secondary-default-background;
  color: $type-secondary-default-text;
  border: $type-secondary-default-border;
}

.tertiary {
  background: $type-tertiary-default-background;
  color: $type-tertiary-default-text;
  border: $type-tertiary-default-border;
}

.small {
  font: $size-sm-font;
  border-radius: $size-sm-radius;
  padding: $size-sm-padding;
  min-height: $size-sm-min-height;
  gap: $size-sm-gap;
}

.extraSmall {
  font: $size-xs-font;
  border-radius: $size-xs-radius;
  padding: $size-xs-padding;
  min-height: $size-xs-min-height;
  gap: $size-xs-gap;
}

.medium {
  font: $size-md-font;
  border-radius: $size-md-radius;
  padding: $size-md-padding;
  min-height: $size-md-min-height;
  gap: $size-md-gap;
}

.large {
  font: $size-lg-font;
  border-radius: $size-lg-radius;
  padding: $size-lg-padding;
  min-height: $size-lg-min-height;
  gap: $size-lg-gap;
}

// TODO: Do we need this class selector?
// .iconOnly {
//   padding: $size-sm $size-md;
// }

/* TODO: Implement properly; Temp implementation, missed tokens */
.overlay {
  /* stylelint-disable-next-line color-no-hex */
  background: #1107297d;
}

.truncatedLabel {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.solid {
  border: none;
}

.bordered {
  border-width: $border-width-default;
}
