'use client'
import type { Size } from '@repo/constants/common/sizes'
import type { ComponentPropsWithRef, ElementType } from 'react'
import { clsx } from 'clsx'
import Image from 'next/image'
import { LoadingIndicator } from '@components/LoadingIndicator'
import { Button as HeroUIButton } from '@heroui/button'
import type { ButtonProps as HeroUIButtonProps } from '@heroui/button'
import { extendVariants } from '@heroui/system'
import styles from '@components/Button/Button.module.scss'
import { Color } from '@repo/constants/common/colors'

const BaseHeroUIButton = extendVariants(HeroUIButton, {
  variants: {
    color: {
      base: '',
    },
    isDisabled: {
      true: 'opacity-100 cursor-not-allowed',
    },
    isLoading: {
      true: 'opacity-100',
    },
    size: {
      base: '',
    },
  },
  defaultVariants: {
    color: 'base',
    size: 'base',
  },
})

export interface IBaseButtonProps
  extends Omit<
    HeroUIButtonProps,
    'color' | 'size' | 'startContent' | 'endContent' | 'isIconOnly' | 'radius' | 'spinnerPlacement' | 'disableRipple'
  > {
  label?: string
  onClick?: () => void
  disabled?: boolean
  className?: string
  href?: string
  size?: Size
  icon?: string
  isTruncated?: boolean
}

const BaseButton = ({ label, isTruncated = true, className, icon, size, ...props }: IBaseButtonProps) => {
  const { ref, ...buttonProps } = props // Remove ref from props to avoid conflicts

  return (
    <BaseHeroUIButton
      className={clsx(
        'hover:!opacity-100',
        className,
        isTruncated && 'min-w-0',
        props.variant === 'solid' && styles.solid,
        props.variant === 'bordered' && styles.bordered,
      )}
      disableRipple={false}
      {...(icon && {
        startContent: (
          <Image
            suppressHydrationWarning
            src={icon}
            alt="Button Icon"
            width={16}
            height={16}
            style={{ opacity: props.isDisabled ? 0.5 : 1 }}
          />
        ),
      })}
      spinner={<LoadingIndicator size={size} />}
      size="base"
      {...buttonProps}>
      {label ? <span className={styles.truncatedLabel}>{label}</span> : props.children}
    </BaseHeroUIButton>
  )
}

export interface IButtonProps extends IBaseButtonProps {
  color?: Color
}

type PolymorphicButtonProps<T extends ElementType = 'button'> = IButtonProps & {
  as?: T
} & Omit<ComponentPropsWithRef<T>, keyof IButtonProps>

export type ButtonProps<T extends ElementType = 'button'> = PolymorphicButtonProps<T>

const getColorClass = (color: IButtonProps['color'] | undefined) => {
  switch (color) {
    case 'primary':
      return styles.primary
    case 'secondary':
      return styles.secondary
    case 'tertiary':
      return styles.tertiary
    case 'overlay': // TODO: Implement properly; Temporary implementation, missed tokens
      return styles.overlay
    default:
      return styles.primary
  }
}

const getSizeClass = (size: IButtonProps['size'] | undefined) => {
  switch (size) {
    case 'xs':
      return styles.extraSmall
    case 'md':
      return styles.medium
    case 'sm':
      return styles.small
    case 'lg':
      return styles.large
    default:
      return styles.medium
  }
}

const Button = <T extends ElementType = any>({ color, className, ...props }: ButtonProps<T>) => {
  const colorClass = getColorClass(color)
  const sizeClass = getSizeClass(props.size)
  // const isIconOnly = !!props.icon && !props.label

  return (
    <BaseButton
      {...props}
      className={clsx(
        colorClass,
        sizeClass,
        // isIconOnly && styles.iconOnly,
        className,
      )}
    />
  )
}

export default Button
