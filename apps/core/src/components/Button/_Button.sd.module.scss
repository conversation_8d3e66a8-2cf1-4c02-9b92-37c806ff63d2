/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$type-primary-default-background: $color-primary;
$type-primary-default-text: $color-on-primary;
$type-primary-default-border: $border-width-default solid $color-primary-border;
$type-primary-default-elevation: $elevation-level-0;
$type-primary-hover-border: $border-width-default solid $color-primary-border;
$type-primary-hover-background: $color-primary-focus;
$type-primary-hover-text: $color-on-primary;
$type-primary-hover-elevation: $elevation-level-0;
$type-primary-disabled-background: $color-surface-400;
$type-primary-disabled-text: $color-surface-700;
$type-secondary-default-background: $color-secondary;
$type-secondary-default-text: $color-on-secondary;
$type-secondary-default-border: $border-width-default solid $color-secondary-border;
$type-secondary-default-elevation: $elevation-level-1;
$type-secondary-hover-border: $border-width-default solid $color-secondary-border;
$type-secondary-hover-background: $color-secondary-focus;
$type-secondary-hover-text: $color-on-secondary;
$type-secondary-hover-elevation: $elevation-level-1;
$type-secondary-disabled-background: $color-surface-400;
$type-secondary-disabled-text: $color-surface-700;
$type-tertiary-default-background: $color-tertiary;
$type-tertiary-default-text: $color-on-tertiary;
$type-tertiary-default-border: $border-width-default solid $color-tertiary-border;
$type-tertiary-default-elevation: $elevation-level-1;
$type-tertiary-hover-border: $border-width-default solid $color-tertiary-border;
$type-tertiary-hover-background: $color-tertiary-focus;
$type-tertiary-hover-text: $color-on-tertiary;
$type-tertiary-hover-elevation: $elevation-level-1;
$type-tertiary-disabled-background: $color-surface-400;
$type-tertiary-disabled-text: $color-surface-700;
$type-text-default-background: $color-transparent;
$type-text-default-text: $color-surface-800;
$type-text-hover-background: $color-transparent;
$type-text-hover-text: $color-surface-900;
$size-xs-font: $typography-label-xs;
$size-xs-radius: $radius-sm;
$size-xs-gap: $size-xxs;
$size-xs-padding: $size-xxs $size-xxs;
$size-xs-min-height: $size-xl;
$size-sm-font: $typography-label-sm;
$size-sm-radius: $radius-xs;
$size-sm-gap: $size-xs;
$size-sm-padding: $size-xs $font-size-sm;
$size-sm-min-height: $size-2xl;
$size-md-radius: $size-xs;
$size-md-font: $typography-label-md;
$size-md-gap: $size-xs;
$size-md-padding: $size-sm $size-lg;
$size-md-min-height: $size-3xl-2;
$size-lg-font: $typography-label-md;
$size-lg-radius: $radius-md;
$size-lg-gap: $size-xs;
$size-lg-padding: $size-sm $size-xl;
$size-lg-min-height: $size-3xl;