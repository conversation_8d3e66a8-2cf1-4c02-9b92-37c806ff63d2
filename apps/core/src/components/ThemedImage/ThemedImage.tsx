'use client'
import { memo, useEffect, useState } from 'react'
import type { ImageProps } from 'next/image'
import Image, { getImageProps } from 'next/image'
import { useTheme } from 'next-themes'
import { preload as preloadSrc } from 'react-dom'

interface IThemedImageProps extends Omit<ImageProps, 'src'> {
  darkSrc?: string
  lightSrc?: string
  preload?: 'light' | 'dark' | 'both'
}

function ThemedImage({ darkSrc, lightSrc, preload, ...props }: IThemedImageProps) {
  const { resolvedTheme } = useTheme()
  const [src, setSrc] = useState<string | undefined>()

  useEffect(() => {
    if (resolvedTheme === 'light') {
      setSrc(lightSrc || darkSrc || src)
    } else if (resolvedTheme === 'dark') {
      setSrc(darkSrc || lightSrc || src)
    }
  }, [darkSrc, lightSrc, resolvedTheme, src])

  if (!src) {
    return null
  }

  if (preload) {
    if ((preload === 'light' || preload === 'both') && lightSrc) {
      const imageProps = getImageProps({ src: lightSrc, ...props })
      preloadSrc(imageProps.props.src, {
        as: 'image',
      })
    }
    if ((preload === 'dark' || preload === 'both') && darkSrc) {
      const imageProps = getImageProps({ src: darkSrc, ...props })
      preloadSrc(imageProps.props.src, {
        as: 'image',
      })
    }
  }

  const style = {
    ...props.style,
    height: props.height,
  }

  return (
    <Image
      className="animate-in fade-in duration-300"
      key={!src ? 'loading' : 'loaded'}
      {...props}
      style={style}
      src={src}
    />
  )
}

export default memo(ThemedImage)
