@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.banner {
  height: calculate-rem(481px);
  width: 100%;
  position: relative;
  overflow: hidden;
  // background: linear-gradient(90deg, $color-background, $color-primary-focus);
  border-radius: clamp(calculate-rem(16px), 2vw, calculate-rem(32px));
  overflow: hidden;

  .bannerOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    padding: calculate-rem(10px);
  }
}

.bannerContent {
  max-width: calculate-rem(364px);
  background-color: alpha($color-surface-500, 0.5);
  backdrop-filter: blur(8px);
  border-radius: calculate-rem(12px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: calculate-rem(360px);
  padding: calculate-rem(16px);
  text-align: center;
  width: 100%;
  color: $color-on-tertiary;
}
