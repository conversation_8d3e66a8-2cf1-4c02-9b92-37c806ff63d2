'use client'
import React from 'react'
import { clsx } from 'clsx'
import type { LinkProps } from 'next/link'
import Link from 'next/link'
import { envVars } from '@/env'
import { useLocale } from '@/hooks/locale'
import type { Locale } from '@constants/locale'
import { normalizePath } from '@repo/helpers/urlHelpers'
import styles from '@components/DynamicLink/DynamicLink.module.scss'

interface IDynamicLinkProps extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps>, LinkProps {
  children: React.ReactNode
  LinkComponent?: React.ElementType
  href: string
  withOpacity?: boolean
}

const DynamicLink: React.FC<IDynamicLinkProps> = ({
  href = '',
  LinkComponent = Link,
  children,
  className,
  withOpacity,
  ...props
}) => {
  let locale = useLocale()
  if (envVars.NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH && locale === envVars.NEXT_PUBLIC_DEFAULT_LOCALE) {
    locale = '' as Locale
  }
  const isExternalLink = href.startsWith('http') || href.startsWith('//')
  const formattedHref = href.startsWith('/') ? href : `/${href}`
  const dynamicHref = isExternalLink ? href : normalizePath(`/${locale}${formattedHref}`)

  return (
    <LinkComponent
      href={dynamicHref}
      target={isExternalLink ? '_blank' : undefined}
      rel={isExternalLink ? 'noopener noreferrer' : undefined}
      className={clsx(
        props.role !== 'button' && styles.link,
        (typeof children === 'string' || withOpacity) && styles.withOpacity,
        className,
      )}
      prefetch
      {...props}>
      {children}
    </LinkComponent>
  )
}

export default DynamicLink
