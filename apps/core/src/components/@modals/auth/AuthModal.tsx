'use client'
import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'
import { withSuspense } from '@/HOC/withSuspense'
import { getAppRouter } from '@/services/router.service'
import { Spinner } from '@components/Spinner'
import { Modal, ModalContent, ModalHeader, ModalBody } from '@heroui/modal'
import type { IAuthFormProps } from '@modules/auth'

// Preload the form component for better performance
const DynamicAuthForm = dynamic(() => import('@modules/auth').then(mod => mod.AuthForm), {
  ssr: false,
  loading: () => <Spinner />,
})

type AuthModalProps = {
  tab: IAuthFormProps['selected']
}

export function AuthModal({ tab }: AuthModalProps) {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(true)
  const [selected, setSelected] = useState(tab)

  const handleClose = () => {
    setIsOpen(false)
    setTimeout(router.back, 200)
  }

  const handleAuthSuccess = () => {
    setIsOpen(false)
    // Navigate to a success page or dashboard
    setTimeout(() => router.replace(getAppRouter().home), 200)
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      placement="bottom-center"
      size="lg"
      scrollBehavior="inside"
      backdrop="blur"
      classNames={{
        base: 'max-h-[90vh]',
        wrapper: 'px-4 pb-4',
      }}>
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          {selected === 'login' ? 'Sign in to your account' : 'Sign up for an account'}
        </ModalHeader>
        <ModalBody className="min-h-[200px]">
          {withSuspense(
            <DynamicAuthForm onSuccess={handleAuthSuccess} selected={selected} onSelectionChange={setSelected} />,
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}
