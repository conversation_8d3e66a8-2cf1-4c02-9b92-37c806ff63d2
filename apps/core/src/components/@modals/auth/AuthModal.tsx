'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getAppRouter } from '@/services/router.service'
import { ModalPortal } from '@components/ModalPortal'
import { Modal, ModalContent, ModalHeader, ModalBody, useDisclosure } from '@heroui/modal'
import { AuthForm, type IAuthFormProps } from '@modules/auth'

type AuthModalProps = {
  tab: IAuthFormProps['selected']
}

export function AuthModal({ tab }: AuthModalProps) {
  const router = useRouter()
  const [selected, setSelected] = useState(tab)
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure()

  const handleClose = () => {
    setTimeout(router.back, 300)
  }

  const handleAuthSuccess = () => {
    onClose()
    // Navigate to a success page or dashboard
    setTimeout(() => router.replace(getAppRouter().home), 300)
  }

  useEffect(() => {
    window.dispatchEvent(new CustomEvent('drawer:close')) // prevent overlapping with sidebar
    onOpen()
  }, [onOpen])

  return (
    <ModalPortal>
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        onClose={handleClose}
        placement="bottom-center"
        size="lg"
        scrollBehavior="inside"
        backdrop="blur"
        classNames={{
          base: 'max-h-[90vh] my-0 mx-0 rounded-b-none md:rounded-b-lg',
          wrapper: 'px-0 md:px-4 pb-0 md:pb-4',
        }}>
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            {selected === 'login' ? 'Sign in to your account' : 'Sign up for an account'}
          </ModalHeader>
          <ModalBody className="min-h-[200px]">
            <AuthForm onSuccess={handleAuthSuccess} selected={selected} onSelectionChange={setSelected} />
          </ModalBody>
        </ModalContent>
      </Modal>
    </ModalPortal>
  )
}
