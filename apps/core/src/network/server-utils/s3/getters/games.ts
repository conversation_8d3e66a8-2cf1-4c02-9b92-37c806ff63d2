import 'server-only'
import { auth } from '@/auth'
import { gamesService } from '@/services/Games.service'
import { getLicenseByLocale } from '@/utils/locale'
import { detectCountryByIp, detectLocaleByIp } from '@/utils/server/network'
import type { Locale } from '@constants/locale'
import type { IGame } from '@repo/types/games'

export const getAvailableGames = async (locale: Locale) => {
  console.log('getAvailableGames for locale:', locale)
  if (!locale) {
    console.warn('Locale is not provided, returning empty games list')
    return [] as IGame[]
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return [] as IGame[]
  }

  const countryByIp = await detectCountryByIp()
  const session = await auth()

  return gamesService.prepareGames(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: undefined,
    license: getLicenseByLocale(locale),
  })
}

export const getAvailableGamesMapById = async (locale: Locale) => {
  if (!locale) {
    console.warn('Locale is not provided, returning empty games list')
    return {} as ReturnType<typeof gamesService.prepareGamesMapById>
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return {} as ReturnType<typeof gamesService.prepareGamesMapById>
  }

  const countryByIp = await detectLocaleByIp()
  const session = await auth()

  return gamesService.prepareGamesMapById(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: undefined,
    license: getLicenseByLocale(locale),
  })
}

export const getAvailableGamesMapBySlug = async (locale: Locale) => {
  if (!locale) {
    console.warn('Locale is not provided, returning empty games list')
    return {} as ReturnType<typeof gamesService.prepareGamesMapBySlug>
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return {} as ReturnType<typeof gamesService.prepareGamesMapBySlug>
  }

  const countryByIp = await detectLocaleByIp()
  const session = await auth()

  return gamesService.prepareGamesMapBySlug(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: undefined,
    license: getLicenseByLocale(locale),
  })
}
