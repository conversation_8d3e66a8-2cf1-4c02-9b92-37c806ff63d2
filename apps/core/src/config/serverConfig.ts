import { envVars } from '@/env'
import type { Locale } from '@constants/locale'

export const serverConfig = {
  appName: envVars.NEXT_PUBLIC_APP_NAME,
  supportedLocales: envVars.SUPPORTED_LOCALES as Locale[],
  defaultLocale: envVars.NEXT_PUBLIC_DEFAULT_LOCALE as Locale,
  assetsBaseUrl: envVars.NEXT_PUBLIC_S3_URL || 'unknown_NEXT_PUBLIC_S3_URL',
  publicRoutes: ['/', '/login', '/register', '/casino', '/live-casino', '/promotions', '/game'],
}
