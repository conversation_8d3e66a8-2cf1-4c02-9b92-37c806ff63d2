#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import parseArgs from './helpers/parseArgs.mjs'

/**
 * <PERSON>ript to convert JSON config to typed format
 * Usage: node scripts/convert-config.mjs --input=<path-to-input-json> [--output=<path-to-output-json>]
 */

const args = parseArgs(process.argv.slice(2))

if (args.help) {
  console.log(`
Usage: node scripts/convert-config.mjs --input=<path-to-input-json> [--output=<path-to-output-json>]

Options:
  --input   Path to the input JSON file (required)
  --output  Path to the output JSON file (optional, defaults to input directory with same name + '-RL-format.json')
  --help    Show this help message

Examples:
  node scripts/convert-config.mjs --input=packages/api/data/s3/configs/game-page-config-global.json
  node scripts/convert-config.mjs --input=game-config.json --output=converted-RL-format.json
`)
  process.exit(0)
}

if (!args.input) {
  console.error('❌ Error: --input parameter is required')
  console.log('Use --help for usage information')
  process.exit(1)
}

/**
 * Wraps a value in the typed format with type and value properties
 * @param {any} value - The value to wrap
 * @param {string} note - Optional note to add
 * @returns {Object} - The wrapped value
 */
function wrapWithType(value, note = '') {
  if (value === null || value === undefined) {
    return { type: 'text', value: '', note }
  }

  if (typeof value === 'string') {
    return { type: 'text', value, note }
  }

  if (typeof value === 'number') {
    return { type: 'number', value, note }
  }

  if (typeof value === 'boolean') {
    return { type: 'boolean', value, note }
  }

  if (Array.isArray(value)) {
    return {
      type: 'array',
      value: value.map(item => {
        // For primitive values (numbers, strings, booleans), wrap them with their correct type
        if (typeof item === 'number') {
          return { type: 'number', value: item }
        }
        if (typeof item === 'string') {
          return { type: 'text', value: item }
        }
        if (typeof item === 'boolean') {
          return { type: 'boolean', value: item }
        }
        // For objects, wrap as object type
        return {
          type: 'object',
          value: convertObjectToTyped(item),
        }
      }),
      note,
    }
  }

  if (typeof value === 'object') {
    return {
      type: 'object',
      value: convertObjectToTyped(value),
      note,
    }
  }

  return { type: 'text', value: String(value), note }
}

/**
 * Converts a plain object to typed format recursively
 * @param {Object} obj - The object to convert
 * @returns {Object} - The converted object with typed values
 */
function convertObjectToTyped(obj) {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    return obj
  }

  const result = {}

  for (const [key, value] of Object.entries(obj)) {
    result[key] = wrapWithType(value)
  }

  return result
}

/**
 * Converts a config to typed format structure without changing content
 * @param {Object} config - The input configuration object
 * @returns {Object} - The converted configuration with typed structure
 */
function convertToTypedFormat(config) {
  // Clone the original config to avoid mutation
  const workingConfig = JSON.parse(JSON.stringify(config))

  // Convert to typed format while preserving all original content
  return convertObjectToTyped(workingConfig)
}

/**
 * Main execution function
 */
async function main() {
  try {
    const inputPath = path.resolve(args.input)

    // Check if input file exists
    if (!fs.existsSync(inputPath)) {
      console.error(`❌ Error: Input file does not exist: ${inputPath}`)
      process.exit(1)
    }

    // Determine output path
    const outputPath = args.output
      ? path.resolve(args.output)
      : path.join(path.dirname(inputPath), path.basename(inputPath, '.json') + '-RL-format.json')

    console.log(`📖 Reading input file: ${inputPath}`)

    // Read and parse input JSON
    const inputContent = fs.readFileSync(inputPath, 'utf8')
    let inputConfig

    try {
      inputConfig = JSON.parse(inputContent)
    } catch (parseError) {
      console.error(`❌ Error: Invalid JSON in input file: ${parseError.message}`)
      process.exit(1)
    }

    console.log(`🔄 Converting config format...`)

    // Convert the configuration
    const convertedConfig = convertToTypedFormat(inputConfig)

    // Write output file
    const outputContent = JSON.stringify(convertedConfig, null, 2)
    fs.writeFileSync(outputPath, outputContent, 'utf8')

    console.log(`✅ Successfully converted and saved to: ${outputPath}`)
    console.log(`📊 Summary:`)
    console.log(`   • Component: ${convertedConfig.component?.value || 'unknown'}`)
    console.log(`   • ID: ${convertedConfig.id?.value || 'unknown'}`)
    console.log(`   • Layout: ${convertedConfig.meta?.value?.layout?.value || 'not specified'}`)
    console.log(`   • Items count: ${convertedConfig.meta?.value?.items?.value?.length || 0}`)

    if (convertedConfig.meta?.value?.items?.value?.length) {
      console.log(`   • Item IDs:`)
      convertedConfig.meta.value.items.value.forEach((item, index) => {
        const itemValue = item.value || item
        const id = itemValue.id?.value || 'unnamed'
        const component = itemValue.component?.value || 'unknown'
        console.log(`     ${index + 1}. ${id} (${component})`)
      })
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`)
    process.exit(1)
  }
}

// Run the script
main()
