export interface GameLite {
  id: number
  slug: string
  name: string
}

export interface GamePosition {
  id: number
  position: Record<string, number>
}

interface Localisation {
  [key: string]: LocalisationMeta
}

interface LocalisationMeta {
  meta: {
    thumbnail: {
      src: string
    }
    animatedThumbnail?: {
      src: string
    }
    sticker?: {
      src: string
    }
  }
  name: string
}

export interface IGame {
  name: string
  id: number
  slug: string
  desktopGameId: string
  mobileGameId: string
  meta: {
    thumbnail: {
      src: string
    }
    animatedThumbnail?: {
      src: string
    }
    sticker?: {
      src: string
    }
  }
  licenses: {
    id: number
    key: string
    name: string
  }[]
  aspectRatio: string
  hasJackpot: boolean
  demoModeLoggedIn: boolean
  demoModeLoggedOut: boolean
  isLiveGame: boolean
  provider: {
    logo: string
    meta: {
      vendorId: string
    }
    name: string
    aggregator: string
    externalKey: string
  }
  tags: { id: number; name: string; type: number }[]
  category: {
    id: number
    name: string
  }
  positions: Record<string, number>
  restrictions: GameRestriction[]
  localisation: Localisation
  showGameName?: boolean
  maxJackpot: number
  hasInstantBonus?: boolean
  instantBonusDetails?: InstantBonus
  seoPage: GameSeoPageInfo | null
  jackpotAmount: {
    [key: string]: string[]
  }
}

export interface JackpotGame {
  gameCategoryId: string | number
  gameDescription: string
  gameId: string | number
  gameTypeId: string | number
  jackpotAmount: string[]
  labelId: string
  partnerId: string
  status: string
  subVendorId: string
  vendorId: string
}

export interface JackpotGamesPayload {
  data: {
    gameDetailList: JackpotGame[]
    errorCode: string | null
  }
}

export interface GameLaunchInfo {
  aspectRatio: string
  categories: number[]
  demoModeLoggedIn: boolean
  demoModeLoggedOut: boolean
  hasFeatureSpins: boolean
  hasFlexibleSpins: boolean
  hasFreeSpins: boolean
  hasJackpot: boolean
  hasTournament: boolean
  id: number
  isLiveGame: boolean
  launchUrl: string
  lobbyURL?: string
  localisation: Localisation | LocalisationMeta
  meta: {
    thumbnail: {
      src: string
    }
  }
  desktopGameId: string
  mobileGameId: string
  provider: {
    logo: string
    meta: {
      vendorId: string
    }
    name: string
    aggregator: string
    externalKey: string
  }
  name: string
  sessionConfig: Record<string, string>
  slug: string
  tags: number[]
  technology: string[]
  server?: string
  clientId?: string
  tokenAuth?: string
  lng?: string
  game_id?: string
  exitUrl?: string
  gameName?: string
  gameVendor?: string
  aggregator?: string
}

export type GameMapId = { [key: number]: Game }

export interface GamesDataPayload extends Partial<RlPaginationResponse<Game>> {
  gamesLimit?: number
  showGamesNames?: boolean
}

export interface SearchCategoryTab {
  show: boolean
  excludedTags?: number[]
}

export interface SearchCategoryTabs {
  allGames: SearchCategoryTab
  liveGames: SearchCategoryTab
  features: SearchCategoryTab
  gameStudios: SearchCategoryTab
}

export interface MainPageGames {
  [key: string]: {
    section: string
    isLoading: boolean
    error: boolean
    gamesList: { payload: Game[] }
  }
}

export interface GameProviders {
  [key: string]: {
    logo: string
    name: string
    aggregator: string
    externalKey: string
    meta: { vendorId: string }
  }
}

export interface MappedGameId {
  id: number
  position: number
}

export interface GameCategory {
  id: number
  name: string
}

export interface GameRestrictionCondition {
  fact: string
  value: any[]
  operator: string
}

export interface GameRestriction {
  licenses: string[]
  conditions: {
    any: GameRestrictionCondition[]
  }
}

export interface GameDetails {
  licenses: GameLicense[]
  positions: {
    [key: string]: number
  }
  restrictions: GameRestriction[]
}

export interface GameSeoPageInfo {
  id: number
  name: string
  noFollow: boolean
  noIndex: boolean
  slug: string
  markets: BoundedSeoPageMarketsInfo
}

export interface GameLicense {
  id: number
  key: string
  name: string
}

export interface GameThumbMeta {
  showGameName?: boolean
  showSkeleton?: boolean
  className?: string
  showTags?: boolean
  showJackpot?: boolean
  tags?: number[]
  shouldBeJackpotDisplayed?: boolean
  isSeoPageGamePreview?: boolean
}

export interface GameThumbOptions {
  showGamesNames?: boolean
  categoryId?: number
  isLastPlayedGame?: boolean
}

export interface Vendor {
  name: string
  key: string
}

export interface GamesResult {
  filteredGames: GameDetails[]
  blockedGames: GameDetails[]
  error?: unknown
}

export interface Interval {
  value: number
  unit: string
}

export interface GetGamesParams {
  countryOfRegistration: string
  countryByIP: string
  /** @deprecated Not used parameter */
  regionOfAction: string
  license: string
  applyMarketRestrictions?: boolean
}

export interface LiveStreamType {
  type: string
  streamerId: string
  sessionId: string
  streamerNickname: string
  totalPlayersInBetBehind: number
  language: string
  startTimestamp: number
  viewers: number
  currentGame: string
  currentGameTitle: string
  accountId: string
  streamerStreamId: string
  gameStreamId: string
  isSingleStream: boolean
  name: string
  description: string
  order: number
  isTesting: boolean
  metadata: string
  contentLanguage: string
  locale: string
  thumbnail: string
  streamerImage: string
  streamerNameImage: string
  streamEventImage: string | null
  streamEventAccentColor: string | null
}

export interface SSEJackpotsDataPayload {
  props: { gameDetailList: JackpotGame[] }
}
