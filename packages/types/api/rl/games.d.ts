import { Locale } from '@constants/locale'
export interface IGetRlGameProps {
  locale: Locale
  license: string
  isLoggedIn: boolean
  slug: string
  userCountryCode?: string
}

export interface IRlGame {
  id: number
  name: string
  slug: string
  desktopGameId: string
  mobileGameId: string
  meta: {
    thumbnail: {
      src: string
    }
  }
  aspectRatio: string
  technology: Array<string>
  hasFreeSpins: boolean
  hasFlexibleSpins: boolean
  hasFeatureSpins: boolean
  hasTournament: boolean
  hasJackpot: boolean
  demoModeLoggedIn: boolean
  demoModeLoggedOut: boolean
  isLiveGame: boolean
  categories: Array<number>
  tags: Array<any>
  provider: {
    meta: {}
    name: string
    aggregator: any
    externalKey: string
    localisation: {
      name: string
    }
  }
  localisation: {
    meta: {
      thumbnail: {
        src: string
      }
    }
    name: string
    demoModeLoggedIn: boolean
    demoModeLoggedOut: boolean
  }
  launchUrl: string
  sessionConfig: any
}
