import * as React from 'react'
import { cn } from '@repo/ui/utils/class'
import * as RadixDialog from '@radix-ui/react-dialog'

function Dialog({ ...props }: React.ComponentProps<typeof RadixDialog.Root>) {
  return <RadixDialog.Root data-slot="dialog" {...props} />
}

function DialogTrigger({ ...props }: React.ComponentProps<typeof RadixDialog.Trigger>) {
  return <RadixDialog.Trigger data-slot="dialog-trigger" {...props} />
}

function DialogClose({ ...props }: React.ComponentProps<typeof RadixDialog.Close>) {
  return <RadixDialog.Close data-slot="dialog-close" {...props} />
}

function DialogPortal({ ...props }: React.ComponentProps<typeof RadixDialog.Portal>) {
  return <RadixDialog.Portal data-slot="dialog-portal" {...props} />
}

function DialogOverlay({ className, ...props }: React.ComponentProps<typeof RadixDialog.Overlay>) {
  return (
    <RadixDialog.Overlay
      data-slot="dialog-overlay"
      className={cn(
        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',
        className,
      )}
      {...props}
    />
  )
}

function DialogContent({
  className,
  children,
  side = 'right',
  ...props
}: React.ComponentProps<typeof RadixDialog.Content> & {
  side?: 'top' | 'right' | 'bottom' | 'left'
}) {
  const closeRef = React.useRef<HTMLButtonElement>(null)

  React.useEffect(() => {
    const handleModalOpen = () => {
      closeRef.current?.click()
    }

    window.addEventListener('drawer:close', handleModalOpen)

    return () => {
      window.removeEventListener('drawer:close', handleModalOpen)
    }
  }, [])

  return (
    <DialogPortal>
      <DialogOverlay
        onClick={() => {
          // fix a bug whe need to click overlay twice after heroui button pressed
          closeRef.current?.click()
        }}
      />
      <RadixDialog.Content
        data-slot="dialog-content"
        className={cn(
          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-300',
          side === 'right' &&
            'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm',
          side === 'left' &&
            'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm',
          className,
        )}
        {...props}>
        {children}
        <DialogClose ref={closeRef} style={{ display: 'none' }} />
        {/* required by DialogContent */}
        <DialogTitle></DialogTitle>
      </RadixDialog.Content>
    </DialogPortal>
  )
}

function DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot="dialog-header" className={cn('flex flex-col gap-1.5 p-4', className)} {...props} />
}

function DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot="dialog-footer" className={cn('mt-auto flex flex-col gap-2 p-4', className)} {...props} />
}

function DialogTitle({ className, ...props }: React.ComponentProps<typeof RadixDialog.Title>) {
  return (
    <RadixDialog.Title data-slot="dialog-title" className={cn('text-foreground font-semibold', className)} {...props} />
  )
}

function DialogDescription({ className, ...props }: React.ComponentProps<typeof RadixDialog.Description>) {
  return (
    <RadixDialog.Description
      data-slot="dialog-description"
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  )
}

export { Dialog, DialogTrigger, DialogClose, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription }
