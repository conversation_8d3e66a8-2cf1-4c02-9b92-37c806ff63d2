class RLEndpoints {
  get banners() {
    return '/banners'
  }
  get login() {
    return '/auth/login'
  }
  get refreshToken() {
    return '/auth/token/refresh'
  }
  get promotions() {
    return '/promotions'
  }
  get bonus() {
    return '/bonus'
  }
  get activeBonusesForEcr() {
    return '/bonus/getActiveBonusListForECR'
  }
  rlGame({ isDemo, slug }: { isDemo?: boolean; slug: string }) {
    return `/games/launch/${isDemo ? 'demo/' : ''}${slug}`
  }
}

export const rlEndpoints = new RLEndpoints()
