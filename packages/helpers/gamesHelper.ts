import type { TopLevelCondition } from 'json-rules-engine'
import { Engine } from 'json-rules-engine'
import { IGame, Localisation } from '@repo/types/games'

function sortGamesByPosition(games: IGame[], countryByIP: string, dir = 'ASC') {
  return [...games].sort((a, b) => {
    const positionA = a.positions?.[countryByIP] ?? a.positions?.ROW ?? null
    const positionB = b.positions?.[countryByIP] ?? b.positions?.ROW ?? null
    if (positionA === null) {
      return 1
    }
    if (positionB === null) {
      return -1
    }
    if (positionA === positionB) {
      return 0
    }
    if ((dir === 'ASC' && positionA < positionB) || (dir === 'DESC' && positionA > positionB)) {
      return -1
    } else if ((dir === 'DESC' && positionA < positionB) || (dir === 'ASC' && positionA > positionB)) {
      return 1
    } else {
      return 0
    }
  })
}

interface IFacts {
  countryOfRegistration?: string
  countryByIp?: string
  regionOfAction?: string
}

async function gameRestrictionsEngine(restrictions: TopLevelCondition, facts: IFacts) {
  const engine = new Engine(undefined, { allowUndefinedFacts: true })
  engine.addRule({
    conditions: restrictions,
    event: {
      type: 'game.checkRestrictions',
    },
  })
  Object.keys(facts).forEach(key => {
    engine.addFact(key, facts[key as keyof IFacts])
  })
  const { failureResults } = await engine.run()
  return !!failureResults.length
}

const checkIsGameValid = async (restrictions: IGame['restrictions'], facts: IFacts, license: string) => {
  if (!restrictions.length) return true

  const checkAllRestrictions: Promise<boolean>[] = []

  for (const rest of restrictions) {
    const licenseFound = rest.licenses.find(l => l === license)
    if (licenseFound && rest.conditions) {
      checkAllRestrictions.push(gameRestrictionsEngine(rest.conditions, facts))
    }
  }

  if (!checkAllRestrictions.length) return false

  const results = await Promise.all(checkAllRestrictions)
  return !results.includes(false)
}

export async function processGamesLocalisation(games: IGame[], localisation: keyof Localisation) {
  return games.map(_game => {
    const game = { ..._game }
    const localisationData = game.localisation?.[localisation]
    game.name = localisationData?.name || game.name
    const meta = localisationData?.meta || game.meta || {}
    meta.thumbnail = localisationData?.meta?.thumbnail || game.meta?.thumbnail
    game.meta = meta
    return game
  })
}

export async function filterGamesRestrictions(
  games: IGame[],
  facts: IFacts,
  license: string,
  countryByIp?: string,
): Promise<IGame[]> {
  const filteredGames: IGame[] = []
  const promises: Promise<any>[] = []
  games.forEach(game => {
    if (!game.restrictions || !game.mobileGameId) {
      return
    }
    promises.push(
      new Promise(async resolve => {
        const isValid = await checkIsGameValid(game.restrictions, facts, license)
        let checkTargetLicenseByMarket
        if (game.licenses.length) {
          checkTargetLicenseByMarket = game.licenses.find(item => item.key === license)
        }
        if (checkTargetLicenseByMarket && isValid) {
          filteredGames.push(game)
        }
        resolve(true)
      }),
    )
  })

  await Promise.all(promises)

  if (!countryByIp) {
    return filteredGames
  }

  return sortGamesByPosition(filteredGames as IGame[], countryByIp) as IGame[]
}

export const getGameThumbUrl = (slug: string) => `/games/${slug}.jpg`

// export function filterLastPlayedGames(
//   { categoryIdsFilters, tagsFilter }: { categoryIdsFilters: number[]; tagsFilter: number[] },
//   gamesList: IRlLastPlayedGame[],
// ) {
//   return gamesList.filter(({ tags, categories }) => {
//     return (
//       categoryIdsFilters?.some(catId => categories.find(c => c === catId)) ||
//       tagsFilter?.some(tagId => tags.find(t => t === tagId))
//     )
//   })
// }

const MIN_LAST_PLAYED_GAMES_COUNT = 4

// export function appendEmptyGameTiles({ games }: { games: IRlLastPlayedGame[] }) {
//   const gamesLength = games?.length
//   if (!gamesLength) {
//     return []
//   }
//   const gamesWithEmptyPlaceholders =
//     gamesLength < MIN_LAST_PLAYED_GAMES_COUNT
//       ? [...games, ...new Array(MIN_LAST_PLAYED_GAMES_COUNT - gamesLength)]
//       : games
//   return gamesWithEmptyPlaceholders
// }

const checkForGameTag = (gamesData: IGame, tag: string) => {
  if (!gamesData) {
    return
  }
  return Object.values(gamesData.tags)?.find(gameTag => gameTag?.id?.toString() === tag || gameTag?.toString() === tag)
}

// export const getWatermarkParams = (game: IGame) => {
//   const state = getState()
//   const vendorsBadges = selectStaticProvidersBadge(state)
//   const generalTmbTag = selectGeneralTmbBadge(state)?.tag || ''
//   const dailyDropsTag = checkForGameTag(game, generalTmbTag)
//   const generalTmbWatermark = selectGeneralTmbBadge(state)?.watermark || ''
//   const imageHost = selectMiddlewareImageHost(state)
//   const sticker = game.meta?.sticker?.src

//   const { provider, isLiveGame } = game
//   const { meta: providerMeta, externalKey } = provider

//   const watermarkFile = dailyDropsTag
//     ? generalTmbWatermark
//     : vendorsBadges?.[(providerMeta?.vendorId as 'tvbet') || externalKey]

//   if ((provider && watermarkFile && (isLiveGame || dailyDropsTag)) || sticker) {
//     return {
//       blend: sticker ? sticker : `${imageHost}/watermarks/${watermarkFile}`,
//       'blend-align': encodeURIComponent('top right'),
//       'blend-mode': 'normal',
//       'blend-h': 0.35,
//     }
//   }
//   return {}
// }

// export const checkIfLiveStream = (gameType?: string) => gameType === LiveStreamsTypes.Type
