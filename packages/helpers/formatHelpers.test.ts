import { formatCompact } from './formatHelpers'

describe('formatCompact', () => {
  it('formats thousands correctly (en-US)', () => {
    expect(formatCompact(109500, 'en-US')).toBe('109.5K')
  })

  it('formats millions correctly (en-US)', () => {
    expect(formatCompact(1200000, 'en-US')).toBe('1.2M')
  })

  it('shows plain numbers for small values', () => {
    expect(formatCompact(999, 'en-US')).toBe('999')
  })

  it('formats correctly for French locale', () => {
    expect(formatCompact(1200000, 'fr-FR')).toBe('1,2 M')
  })
})
